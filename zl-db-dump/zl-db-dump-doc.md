# `zl_db_dump.py` Documentation

This document provides an overview of the functions within the `zl_db_dump.py` script, explaining their purpose and functionality.

## Table of Contents
1.  `load_configuration()`
2.  `normalize_mongo_uri(uri)`
3.  `ensure_mongo_uri_scheme(uri_string)`
4.  `get_cluster_uri(env, cluster_type, service=None)`
5.  `get_credentials(cluster_uri, cluster_type)`
6.  `sanitize_command(cmd)`
7.  `run_command(cmd, env=None)`
8.  `dump_mongo(uri, user, password, db=None, collection=None)`
9.  `dump_postgres(host, user, password, db=None, table=None, schema=None)`
10. `compress_folder(folder_path)`
11. `upload_to_s3(file_path)`
12. `main()`

---

## Usage Examples

This section provides examples of how to use the `zl_db_dump.py` script for various database dump scenarios.

### General Usage

The script is executed via the command line with arguments specifying the target database, environment, and type of dump. The `--service` argument is **required** to specify the database to dump.

```bash
python3 zl_db_dump.py --service <service_name> --env <environment> --cluster <mongo|postgres> [options]
```

### MongoDB Dump Examples

*   **Specific Database Dump:** Dumps all collections from a specific database within the MongoDB cluster.
    ```bash
    python3 zl_db_dump.py --env kalpha --cluster mongo --service n-event-hub
    ```

*   **Multiple Collections Dump:** Dumps specific collections from a database. Provide collection names as a comma-separated list.
    ```bash
    python3 zl_db_dump.py --env kalpha --cluster mongo --service n-event-hub --collection users,orders,products
    ```

### PostgreSQL Dump Examples

*   **Specific Database Dump:** Dumps all tables and data from a specific database within the PostgreSQL cluster.
    ```bash
    python3 zl_db_dump.py --env kalpha --cluster postgres --service n-authentication
    ```

*   **Multiple Tables Dump:** Dumps specific tables from a database. Provide table names as a comma-separated list.
    ```bash
    python3 zl_db_dump.py --env kalpha --cluster postgres --service n-machine-maintenance --table employees,devices
    ```

*   **Specific Schema Dump:** Dumps a specific schema from a database.
    ```bash
    python3 zl_db_dump.py --env kalpha --cluster postgres --service n-event-hub --schema public
    ```

## Function Descriptions

### `load_configuration()`
*   **Purpose:** Loads service mapping and database credentials from predefined file paths. These files are expected to be mounted from Kubernetes ConfigMaps and Secrets.
*   **What it does:**
    *   Attempts to read `service-mapping.json` from `/etc/config/service-mapping.json`.
    *   Attempts to read `db-credentials.json` from `/etc/db-secrets/db-credentials.json`.
    *   Parses the JSON content of these files.
    *   Logs success or fatal errors if files are not found or are malformed.
*   **Returns:** A tuple containing two dictionaries: `services_data` (service mappings) and `credentials_data` (database credentials).

### `normalize_mongo_uri(uri)`
*   **Purpose:** Standardizes MongoDB URIs by removing port numbers. This is primarily used for matching URIs against those stored in the credentials data, where port numbers might vary or be omitted.
*   **Parameters:**
    *   `uri` (str): The MongoDB connection URI.
*   **Returns:** A string with the port numbers removed from the URI.

### `ensure_mongo_uri_scheme(uri_string)`
*   **Purpose:** Ensures that a MongoDB URI string starts with either `mongodb://` or `mongodb+srv://`. If no scheme is present, it prepends `mongodb://`.
*   **Parameters:**
    *   `uri_string` (str): The MongoDB connection URI string.
*   **Returns:** The URI string with the appropriate scheme.

### `get_cluster_uri(env, cluster_type, service)`
*   **Purpose:** Retrieves the connection URI for a specified database cluster based on the environment, cluster type (mongo/postgres), and the service name.
*   **Parameters:**
    *   `env` (str): The environment (e.g., 'kalpha', 'nint').
    *   `cluster_type` (str): The type of database cluster ('mongo' or 'postgres').
    *   `service` (str): The service name (required).
*   **Returns:** The cluster URI string if found, otherwise `None` (and logs an error).

### `get_credentials(cluster_uri, cluster_type)`
*   **Purpose:** Fetches the database credentials (username, password, etc.) for a given cluster URI and type from the loaded `CREDENTIALS_DATA`.
*   **Parameters:**
    *   `cluster_uri` (str): The connection URI of the cluster.
    *   `cluster_type` (str): The type of database cluster ('mongo' or 'postgres').
*   **Returns:** A dictionary containing the credentials if found, otherwise `None` (and logs an error).

### `sanitize_command(cmd)`
*   **Purpose:** Creates a sanitized version of a shell command list for safe logging, specifically redacting passwords.
*   **Parameters:**
    *   `cmd` (list): A list of strings representing the shell command and its arguments.
*   **Returns:** A new list with the password argument (if present) replaced by '***'.

### `run_command(cmd, env=None)`
*   **Purpose:** Executes a given shell command as a subprocess, captures its output, and handles common errors.
*   **Parameters:**
    *   `cmd` (list): A list of strings representing the shell command and its arguments.
    *   `env` (dict, optional): A dictionary of environment variables to set for the command. Useful for `PGPASSWORD`.
*   **Returns:** A tuple `(success: bool, output: str)`. `success` is `True` if the command executed without error, `False` otherwise. `output` contains the stdout of the command or stderr in case of an error.

### `dump_mongo(uri, user, password, db, collection=None)`
*   **Purpose:** Performs a MongoDB dump of a specific database or specific collections within a database.
*   **Parameters:**
    *   `uri` (str): MongoDB connection URI.
    *   `user` (str): MongoDB username.
    *   `password` (str): MongoDB password.
    *   `db` (str): The name of the database to dump (required).
    *   `collection` (list of str, optional): A list of collection names to dump from the specified `db`.
*   **What it does:**
    *   Creates a timestamped dump folder.
    *   Constructs and executes `mongodump` commands.
    *   If `collection` is a list, it runs `mongodump` for each specified collection.
    *   If `collection` is `None`, it dumps the entire specified database.
*   **Returns:** The path to the created dump folder on success, `None` on failure.

### `dump_postgres(host, user, password, db, table=None, schema=None)`
*   **Purpose:** Performs a PostgreSQL dump of a specific database, or specific tables/schemas within a database.
*   **Parameters:**
    *   `host` (str): PostgreSQL host.
    *   `user` (str): PostgreSQL username.
    *   `password` (str): PostgreSQL password.
    *   `db` (str): The name of the database to dump (required).
    *   `table` (list of str, optional): A list of table names to dump from the specified `db`.
    *   `schema` (str, optional): The name of the schema to dump from the specified `db`.
*   **What it does:**
    *   Creates a timestamped dump folder and a `.sql` dump file.
    *   Sets the `PGPASSWORD` environment variable for `pg_dump`.
    *   Executes `pg_dump`. If `table` is a list, it includes multiple `-t` flags. If `schema` is provided, it includes the `-n` flag.
*   **Returns:** The path to the created dump folder on success, `None` on failure.

### `compress_folder(folder_path)`
*   **Purpose:** Compresses a given folder into a `.tar.gz` archive.
*   **Parameters:**
    *   `folder_path` (str): The absolute path to the folder to be compressed.
*   **Returns:** The path to the created `.tar.gz` file on success, `None` if `folder_path` is invalid.

### `upload_to_s3(file_path)`
*   **Purpose:** Uploads a specified file to an S3 bucket and generates a pre-signed URL for accessing the uploaded file.
*   **Parameters:**
    *   `file_path` (str): The absolute path to the file to upload.
*   **What it does:**
    *   Uses the `aws s3 cp` command to upload the file to the S3 bucket defined by `S3_BUCKET_NAME`.
    *   Uses the `aws s3 presign` command to generate a pre-signed URL that expires in 1 hour (3600 seconds).
*   **Returns:** The pre-signed URL string on successful upload and URL generation, `None` on failure.

### `main()`
*   **Purpose:** The main entry point of the script. It parses command-line arguments, orchestrates the database dump process, and handles cleanup.
*   **What it does:**
    *   Parses command-line arguments (`--service` (required), `--env`, `--cluster`, `--collection`, `--table`, `--schema`).
    *   Splits comma-separated `collection` and `table` arguments into lists.
    *   Retrieves cluster URI and credentials using helper functions.
    *   Determines the database name based on `service` and `env`.
    *   Calls either `dump_mongo` or `dump_postgres` based on the `--cluster` argument.
    *   Compresses the resulting dump folder.
    *   Uploads the compressed file to S3.
    *   Logs the pre-signed URL for the uploaded backup.
    *   Cleans up local dump folders and compressed files.
