
import argparse
import json
import os
import subprocess
import tarfile
import re
import shutil
import logging
from datetime import datetime

# --- Basic Logging Configuration ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
)

def load_configuration():
    """Loads service and credential data from mounted files."""
    services_data = {}
    credentials_data = {}

    service_mapping_path = "/etc/config/service-mapping.json"
    db_creds_file_path = "/etc/db-secrets/db-credentials.json"

    # Load service mappings from ConfigMap
    try:
        with open(service_mapping_path, 'r') as f:
            services_data = json.load(f)
        logging.info(f"Successfully loaded service mappings from {service_mapping_path}")
    except FileNotFoundError:
        logging.error(f"FATAL: Service mapping file not found at {service_mapping_path}. Please ensure the ConfigMap is mounted correctly.")
        exit(1)
    except json.JSONDecodeError:
        logging.error(f"FATAL: Could not decode J<PERSON><PERSON> from {service_mapping_path}.")
        exit(1)

    # Load credentials from Secret file
    try:
        with open(db_creds_file_path, 'r') as f:
            credentials_data = json.load(f)
        logging.info(f"Successfully loaded credentials from {db_creds_file_path}")
    except FileNotFoundError:
        logging.error(f"FATAL: Credentials file not found at {db_creds_file_path}. Please ensure the Secret is mounted correctly.")
        exit(1)
    except json.JSONDecodeError:
        logging.error(f"FATAL: Could not decode JSON from {db_creds_file_path}.")
        exit(1)


    return services_data, credentials_data

# --- Configuration Data ---
SERVICES_DATA, CREDENTIALS_DATA = load_configuration()


# S3 Configuration
S3_BUCKET_NAME = "madhav-unique-db-bucket-123"
S3_REGION = "ap-southeast-1"

def normalize_mongo_uri(uri):
    """Removes port numbers from a MongoDB URI for matching purposes."""
    return re.sub(r':\d+', '', uri)

def ensure_mongo_uri_scheme(uri_string):
    """Prepends 'mongodb://' to the string if it doesn't have a scheme."""
    if not uri_string.startswith("mongodb://") and not uri_string.startswith("mongodb+srv://"):
        return f"mongodb://{uri_string}"
    return uri_string

def get_cluster_uri(env, cluster_type, service=None):
    """Gets the cluster URI from the services data, inferring if possible."""
    if service:
        try:
            return SERVICES_DATA[service][env][f"{cluster_type}_cluster"]
        except KeyError:
            logging.error(f"Cluster URI not found for service '{service}', env '{env}', and cluster type '{cluster_type}'.")
            return None

    uris = {
        envs[env][f"{cluster_type}_cluster"]
        for envs in SERVICES_DATA.values()
        if env in envs and f"{cluster_type}_cluster" in envs[env]
    }

    if len(uris) == 1:
        return uris.pop()
    elif len(uris) > 1:
        logging.error(f"Multiple {cluster_type} clusters found for env '{env}'. Please specify a service using --service.")
        return None
    else:
        logging.error(f"No {cluster_type} cluster found for env '{env}'.")
        return None

def get_credentials(cluster_uri, cluster_type):
    """Gets credentials for a given cluster URI."""
    creds_for_type = CREDENTIALS_DATA.get(cluster_type, {})
    if cluster_type == "mongo":
        normalized_uri = normalize_mongo_uri(cluster_uri)
        for key, creds in creds_for_type.items():
            if normalize_mongo_uri(key) == normalized_uri:
                return creds
    else:
        if cluster_uri in creds_for_type:
            return creds_for_type[cluster_uri]

    logging.error(f"Credentials not found for cluster URI '{cluster_uri}'.")
    return None

def sanitize_command(cmd):
    """Returns a sanitized version of a command list for safe logging."""
    safe_cmd = list(cmd)
    try:
        password_index = safe_cmd.index("--password")
        if password_index + 1 < len(safe_cmd):
            safe_cmd[password_index + 1] = '***'
    except ValueError:
        pass  # --password not found
    return safe_cmd

def run_command(cmd, env=None):
    """
    Executes a shell command and handles common errors.
    Returns a tuple of (success: bool, output: str).
    """
    safe_command_str = ' '.join(sanitize_command(cmd))
    logging.info(f"Running command: {safe_command_str}")
    try:
        result = subprocess.run(
            cmd, check=True, capture_output=True, text=True, env=env
        )
        return True, result.stdout
    except FileNotFoundError:
        logging.error(f"Command not found: '{cmd[0]}'. Please ensure it's in your system's PATH.")
        return False, ""
    except subprocess.CalledProcessError as e:
        logging.error(f"Error during command execution: {e.stderr}")
        return False, e.stderr

def dump_mongo(uri, user, password, db=None, collection=None):
    """Dumps a MongoDB database or a specific collection."""
    if not db:
        logging.error("A specific database must be provided. Dumping all databases is disallowed.")
        return None

    timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
    dump_folder = f"mongo_dump_{timestamp}"
    os.makedirs(dump_folder, exist_ok=True)

    full_uri = ensure_mongo_uri_scheme(uri)

    cmd = [
        "mongodump",
        f"--uri={full_uri}",
        f"--username={user}",
        f"--password={password}",
        "--authenticationDatabase=admin",
        f"--out={dump_folder}",
        f"--db={db}",
    ]
    if collection:
        cmd.append(f"--collection={collection}")

    if run_command(cmd)[0]:
        logging.info("MongoDB dump successful.")
        return dump_folder
    return None

def dump_postgres(host, user, password, db=None, table=None, schema=None):
    """Dumps a PostgreSQL database, or a specific table/schema."""
    if not db:
        logging.error("A specific database must be provided. Dumping all databases is disallowed.")
        return None

    timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
    dump_folder = f"postgres_dump_{timestamp}"
    os.makedirs(dump_folder, exist_ok=True)
    
    filename = f"{db}.sql"
    if table:
        filename = f"{db}_{table}.sql"
    elif schema:
        filename = f"{db}_{schema}.sql"
        
    dump_file = os.path.join(dump_folder, filename)
    env = {**os.environ, "PGPASSWORD": password}

    cmd = ["pg_dump", "-h", host, "-U", user, "-d", db, "-f", dump_file]
    if table:
        cmd.extend(["-t", table])
    if schema:
        cmd.extend(["-n", schema])

    if run_command(cmd, env=env)[0]:
        logging.info("PostgreSQL dump successful.")
        return dump_folder
    return None

def compress_folder(folder_path):
    """Compresses a folder into a .tar.gz file."""
    if not folder_path:
        return None
    output_filename = f"{folder_path}.tar.gz"
    with tarfile.open(output_filename, "w:gz") as tar:
        tar.add(folder_path, arcname=os.path.basename(folder_path))
    logging.info(f"Compressed dump to {output_filename}")
    return output_filename

def upload_to_s3(file_path):
    """Uploads a file to an S3 bucket and generates a pre-signed URL using AWS CLI."""
    if not file_path:
        return None

    bucket_name = S3_BUCKET_NAME
    object_key = os.path.basename(file_path)
    s3_uri = f"s3://{bucket_name}/{object_key}"

    logging.info(f"Uploading {file_path} to {s3_uri}")

    upload_cmd = ["aws", "s3", "cp", file_path, s3_uri]
    presign_cmd = ["aws", "s3", "presign", s3_uri, "--expires-in", "3600"]

    upload_success, _ = run_command(upload_cmd)

    if upload_success:
        logging.info(f"Successfully uploaded to {s3_uri}")
        presign_success, presigned_url = run_command(presign_cmd)
        if presign_success:
            logging.info("Successfully generated pre-signed URL.")
            return presigned_url.strip()
        else:
            logging.error("Failed to generate pre-signed URL.")
            return None
    else:
        logging.error(f"S3 upload failed for {file_path}.")
        return None

def main():
    parser = argparse.ArgumentParser(description="Database dump tool for MongoDB and PostgreSQL.")
    parser.add_argument("--service", required=True, help="The service name, which is used to derive the database name (e.g., n-event-hub).")
    parser.add_argument("--env", required=True, help="The environment (e.g., nint, kalpha).")
    parser.add_argument("--cluster", required=True, choices=["mongo", "postgres"], help="The cluster type.")
    parser.add_argument("--collection", help="For MongoDB, the collection to dump. Requires --service.")
    parser.add_argument("--table", help="For PostgreSQL, the table to dump. Requires --service.")
    parser.add_argument("--schema", help="For PostgreSQL, the schema to dump. Requires --service.")

    args = parser.parse_args()

    cluster_uri = get_cluster_uri(args.env, args.cluster, args.service)
    if not cluster_uri:
        return

    creds = get_credentials(cluster_uri, args.cluster)
    if not creds:
        return

    db_name = None
    if args.cluster == 'mongo':
        db_name = args.service.replace('-', '_')
    elif args.cluster == 'postgres':
        db_name = f"{args.env}_{args.service.replace('-', '_')}"

    dump_folder = None
    if args.cluster == "mongo":
        dump_folder = dump_mongo(
            uri=cluster_uri,
            user=creds["admin_user"],
            password=creds["admin_password"],
            db=db_name,
            collection=args.collection,
        )
    elif args.cluster == "postgres":
        dump_folder = dump_postgres(
            host=cluster_uri,
            user=creds["admin_user"],
            password=creds["admin_password"],
            db=db_name,
            table=args.table,
            schema=args.schema,
        )

    compressed_file = None
    if dump_folder:
        compressed_file = compress_folder(dump_folder)
        if compressed_file:
            presigned_url = upload_to_s3(compressed_file)
            if presigned_url:
                logging.info(f"\n\nBackup complete. Access your file using the following pre-signed URL (expires in 1 hour):\n{presigned_url}")

    if compressed_file and os.path.exists(compressed_file):
        os.remove(compressed_file)
    if dump_folder and os.path.exists(dump_folder):
        shutil.rmtree(dump_folder)
    logging.info("Cleaned up local files.")

if __name__ == "__main__":
    main()