import argparse
import boto3
import logging
import sys
import json
import uuid
import configparser

# --- Logger Setup ---
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
)


def create_bucket(bucket_name, region, aws_access_key_id=None, aws_secret_access_key=None):
    """Creates an S3 bucket and applies a 1-day retention policy."""
    try:
        s3_client = boto3.client(
            "s3",
            region_name=region,
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key
        )
        logging.info(f"Creating S3 Bucket: {bucket_name} in region {region}")

        if region == "us-east-1":
            s3_client.create_bucket(Bucket=bucket_name)
        else:
            location = {'LocationConstraint': region}
            s3_client.create_bucket(
                Bucket=bucket_name, CreateBucketConfiguration=location
            )

        logging.info("Bucket created successfully.")

        lifecycle_policy = {
            "Rules": [
                {
                    "ID": "ExpireOldBackups",
                    "Status": "Enabled",
                    "Filter": {"Prefix": ""},
                    "Expiration": {"Days": 1},
                },
                {
                    "ID": "CleanupIncompleteUploads",
                    "Status": "Enabled",
                    "Filter": {"Prefix": ""},
                    "AbortIncompleteMultipartUpload": {"DaysAfterInitiation": 7},
                },
            ]
        }

        logging.info("Applying 1-day retention (lifecycle) policy...")
        s3_client.put_bucket_lifecycle_configuration(
            Bucket=bucket_name, LifecycleConfiguration=lifecycle_policy
        )
        logging.info("Lifecycle policy applied successfully.")
        print(f"\n--- S3 Bucket setup is complete! ---")
        print(f"Bucket Name: {bucket_name}")

    except Exception as e:
        logging.error(f"An error occurred during bucket creation: {e}")
        sys.exit(1)


def delete_bucket(bucket_name, region, aws_access_key_id=None, aws_secret_access_key=None):
    """Deletes all objects and versions in an S3 bucket, then deletes the bucket."""
    try:
        s3 = boto3.resource(
            "s3",
            region_name=region,
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key
        )
        bucket = s3.Bucket(bucket_name)

        # Check if bucket exists before proceeding
        bucket.load()

        logging.warning(f"You are about to permanently delete the S3 bucket: {bucket_name}")
        
        # Getting object counts can be slow on very large buckets, but is good for a final check.
        object_count = sum(1 for _ in bucket.objects.all())
        version_count = sum(1 for _ in bucket.object_versions.all())
        logging.warning(f"This bucket contains approximately {object_count} objects and {version_count} total versions.")
        
        confirm = input("This action cannot be undone. Are you sure? (y/n): ")

        if confirm.lower() != 'y':
            logging.info("Deletion cancelled by user.")
            return

        logging.info(f"Emptying all objects and versions from bucket {bucket_name}... Please wait.")
        bucket.object_versions.delete()
        logging.info("Bucket emptied successfully.")

        logging.info(f"Deleting bucket {bucket_name}...")
        bucket.delete()
        logging.info(f"--- Bucket {bucket_name} has been successfully deleted. ---")

    except s3.meta.client.exceptions.NoSuchBucket:
        logging.error(f"Error: Bucket '{bucket_name}' not found.")
        sys.exit(1)
    except Exception as e:
        logging.error(f"An error occurred during bucket deletion: {e}")
        sys.exit(1)

def main():
    parser = argparse.ArgumentParser(
        description="A tool to create and delete S3 buckets for backups.",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument(
        "--credentials-file",
        help="Path to a file containing AWS credentials (e.g., aws_credentials.ini). NOT recommended for production."
    )
    subparsers = parser.add_subparsers(dest="command", required=True)

    # --- Create Command ---
    parser_create = subparsers.add_parser("create", help="Create a new S3 bucket.")
    parser_create.add_argument(
        "--bucket-name",
        help="A unique name for the S3 bucket. If not provided, a name will be generated."
    )
    parser_create.add_argument(
        "--region", default="ap-southeast-1", help="The AWS region to create the bucket in."
    )

    # --- Delete Command ---
    parser_delete = subparsers.add_parser("delete", help="Delete an existing S3 bucket.")
    parser_delete.add_argument(
        "--bucket-name", required=True, help="The exact name of the bucket to delete."
    )
    parser_delete.add_argument(
        "--region", default="ap-southeast-1", help="The AWS region the bucket is in."
    )

    args = parser.parse_args()

    aws_access_key_id = None
    aws_secret_access_key = None

    if args.credentials_file:
        logging.warning("WARNING: Reading AWS credentials directly from a file is NOT recommended for production environments.")
        logging.warning("This method exposes your keys in plaintext. Use AWS environment variables or IAM roles for better security.")
        config = configparser.ConfigParser()
        try:
            config.read(args.credentials_file)
            if 'default' in config:
                aws_access_key_id = config['default'].get('aws_access_key_id')
                aws_secret_access_key = config['default'].get('aws_secret_access_key')
                if not aws_access_key_id or not aws_secret_access_key:
                    logging.error(f"Credentials file '{args.credentials_file}' is missing 'aws_access_key_id' or 'aws_secret_access_key' in the [default] section.")
                    sys.exit(1)
            else:
                logging.error(f"Credentials file '{args.credentials_file}' is missing a '[default]' section.")
                sys.exit(1)
        except Exception as e:
            logging.error(f"Error reading credentials file '{args.credentials_file}': {e}")
            sys.exit(1)

    if args.command == "create":
        bucket_name = args.bucket_name
        if not bucket_name:
            random_suffix = uuid.uuid4().hex[:8]
            bucket_name = f"zl-db-dumps-{random_suffix}"
            logging.info(f"No bucket name provided. Generated unique name: {bucket_name}")
        create_bucket(bucket_name, args.region, aws_access_key_id, aws_secret_access_key)

    elif args.command == "delete":
        delete_bucket(args.bucket_name, args.region, aws_access_key_id, aws_secret_access_key)

if __name__ == "__main__":
    main()
