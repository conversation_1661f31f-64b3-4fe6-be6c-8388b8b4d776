# `zl-db-dump.py` Script Documentation

This document provides an overview of the `zl-db-dump.py` script, its functions, and how to use it.

## Workflow

The script is designed to dump a specific database from a MongoDB or PostgreSQL cluster, compress it, and upload it to an S3 bucket.

1.  **Parse Command-Line Arguments**: The script starts by parsing user-provided arguments such as `--service`, `--env`, and `--cluster`. The `--service` argument is mandatory to ensure a specific database is targeted.
2.  **Get Cluster URI**: Based on the environment and service, it retrieves the database cluster's URI from the `SERVICES_DATA` configuration.
3.  **Get Credentials**: It fetches the required username and password for the cluster from the `CREDENTIALS_DATA` configuration.
4.  **Determine Database Name**: The target database name is derived from the service name and environment.
5.  **Dump Database**:
    *   For MongoDB, it uses `mongodump` to dump a specific database (or a collection within it).
    *   For PostgreSQL, it uses `pg_dump` to dump a specific database (or a table/schema within it).
    *   Dumping all databases is explicitly disallowed.
6.  **Compress Dump**: The generated dump folder is compressed into a `.tar.gz` archive.
7.  **Upload to S3**: The compressed file is uploaded to the configured S3 bucket using the AWS CLI.
8.  **Generate Pre-signed URL**: After a successful upload, a pre-signed URL for the S3 object is generated, which is valid for one hour.
9.  **Cleanup**: The local dump folder and compressed archive are removed.

## Function Explanations

### `normalize_mongo_uri(uri)`

*   **Description**: Removes port numbers from a MongoDB URI string. This is used for matching URIs in the configuration, allowing for some flexibility in how URIs are specified.
*   **Example**:
    ```python
    normalize_mongo_uri("mongodb://************:27017")
    # Returns: "mongodb://************"
    ```

### `ensure_mongo_uri_scheme(uri_string)`

*   **Description**: Ensures that a MongoDB URI has the `mongodb://` scheme prefix.
*   **Example**:
    ```python
    ensure_mongo_uri_scheme("my-mongo-cluster.com")
    # Returns: "mongodb://my-mongo-cluster.com"
    ```

### `get_cluster_uri(env, cluster_type, service)`

*   **Description**: Retrieves the cluster URI for a given service, environment, and cluster type from the `SERVICES_DATA` configuration.
*   **Example**:
    ```python
    get_cluster_uri("nint", "mongo", "n-event-hub")
    # Returns: "mongodb://************:27017,***********:27017,***********:27017/?replicaSet=eks-mongo-3"
    ```

### `get_credentials(cluster_uri, cluster_type)`

*   **Description**: Fetches the admin username and password for a given cluster URI from the `CREDENTIALS_DATA` configuration. For MongoDB, it uses a normalized URI for matching.
*   **Example**:
    ```python
    get_credentials("postgres.testns", "postgres")
    # Returns: {'port': '5432', 'admin_user': 'myuser', 'admin_password': 'mypassword'}
    ```

### `sanitize_command(cmd)`

*   **Description**: Hides passwords in command logs. It replaces the argument following `--password` with '***'.
*   **Example**:
    ```python
    sanitize_command(["mongodump", "--password", "secret"])
    # Returns: ['mongodump', '--password', '***']
    ```

### `run_command(cmd, env=None)`

*   **Description**: Executes a shell command, logs a sanitized version of it, and captures its output. It handles errors like the command not being found or the command failing.
*   **Example**:
    ```python
    run_command(["ls", "-l"])
    # Executes the command and returns (True, "total 0\n-rw-r--r-- 1 <USER> <GROUP> 0 Aug 12 10:00 file.txt\n")
    ```

### `dump_mongo(uri, user, password, db, collection=None)`

*   **Description**: Dumps a specific MongoDB database or a single collection from it. It requires the `db` parameter to be provided, preventing dumps of the entire MongoDB instance.
*   **Example Usage (in script)**:
    ```bash
    python zl_db_dump.py --service n-event-hub --env nint --cluster mongo
    ```

### `dump_postgres(host, user, password, db, table=None, schema=None)`

*   **Description**: Dumps a specific PostgreSQL database, or a single table or schema from it. It requires the `db` parameter, preventing the use of `pg_dumpall`.
*   **Example Usage (in script)**:
    ```bash
    python zl_db_dump.py --service n-event-hub --env test --cluster postgres --table my_table
    ```

### `compress_folder(folder_path)`

*   **Description**: Compresses the specified folder into a `.tar.gz` archive.
*   **Example**:
    ```python
    compress_folder("mongo_dump_20250812-103000")
    # Creates "mongo_dump_20250812-103000.tar.gz"
    ```

### `upload_to_s3(file_path)`

*   **Description**: Uploads a file to the S3 bucket defined by `S3_BUCKET_NAME` and then generates a 1-hour pre-signed URL for downloading it.
*   **Example**:
    ```python
    upload_to_s3("mongo_dump.tar.gz")
    # Uploads the file and returns a pre-signed URL.
    ```

### `main()`

*   **Description**: The main function that orchestrates the entire process. It parses arguments, calls the other functions in sequence to perform the dump, compression, upload, and cleanup. It now requires the `--service` argument to be provided.

## Test Cases

Here are some test cases to validate the script's functionality.

**Prerequisites**:
*   Ensure `mongodump`, `pg_dump`, and `aws-cli` are installed and in the system's PATH.
*   Ensure your AWS credentials are configured correctly (e.g., via `aws configure` or environment variables).
*   The S3 bucket `madhav-unique-db-bucket-123` must exist.

### Case 1: MongoDB Collection Dump

*   **Objective**: Dump a single collection from a MongoDB database.
*   **Command**:
    ```bash
    python zl_db_dump.py --env nint --service n-event-hub --cluster mongo --collection my_collection
    ```
*   **Expected Outcome**:
    1.  The script connects to the `nint` `n-event-hub` mongo cluster.
    2.  It dumps only the `my_collection` collection from the `n_event_hub` database.
    3.  The dump is compressed, uploaded to S3.
    4.  A pre-signed URL is printed to the console.
    5.  Local dump files are cleaned up.

### Case 2: PostgreSQL Table Dump

*   **Objective**: Dump a single table from a PostgreSQL database.
*   **Command**:
    ```bash
    python zl_db_dump.py --env test --service n-machine-maintenance --cluster postgres --table my_table
    ```
*   **Expected Outcome**:
    1.  The script connects to the `test` `n-machine-maintenance` postgres cluster.
    2.  It dumps only the `my_table` table from the `test_n_machine_maintenance` database.
    3.  The dump is compressed, uploaded to S3.
    4.  A pre-signed URL is printed to the console.
    5.  Local dump files are cleaned up.

### Case 3: PostgreSQL Schema Dump

*   **Objective**: Dump a single schema from a PostgreSQL database.
*   **Command**:
    ```bash
    python zl_db_dump.py --env test --service n-machine-maintenance --cluster postgres --schema my_schema
    ```
*   **Expected Outcome**:
    1.  The script connects to the `test` `n-machine-maintenance` postgres cluster.
    2.  It dumps only the `my_schema` schema from the `test_n_machine_maintenance` database.
    3.  The dump is compressed, uploaded to S3.
    4.  A pre-signed URL is printed to the console.
    5.  Local dump files are cleaned up.

### Case 4: Failure - Missing Service Argument

*   **Objective**: Verify that the script fails if the `--service` argument is not provided.
*   **Command**:
    ```bash
    python zl_db_dump.py --env nint --cluster mongo
    ```
*   **Expected Outcome**:
    1.  The script exits with an error.
    2.  An error message is displayed indicating that the `--service` argument is required.
    3.  No dump operation is performed.

```