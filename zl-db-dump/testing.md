# Guide to Testing the Database Dump Script

This document provides a step-by-step guide to setting up a local test environment and running test cases against the `zl_db_dump.py` script.

---

## 1. Prerequisites

Before you begin, ensure you have the following:

-   `kubectl` installed and configured to access a Kubernetes cluster.
-   A namespace named `testns` available in the cluster.
-   Running pods for PostgreSQL and MongoDB in the `testns` namespace, labeled with `app=postgres` and `app=mongo` respectively.

---

## 2. The Test Environment Setup Script

The following bash script will populate your Kubernetes-based databases with test data. It creates databases, tables, and users required for the test cases.

Save this script as `setup_test_env.sh`:

```bash
#!/bin/bash

# Exit on any error
set -e

# --- Configuration ---
POSTGRES_POD=$(kubectl get pods -n testns -l app=postgres -o jsonpath='{.items[0].metadata.name}')
POSTGRES_ADMIN_USER="myuser"
POSTGRES_ADMIN_DB="mydatabase"
# NOTE: DB names are formatted to match the dump script's logic (env_service-name -> env_service_name)
POSTGRES_DBS=("kalpha_n_authentication" "kalpha_n_machine_maintenance" "kalpha_n_event_hub" "madhva")

MONGO_POD=$(kubectl get pods -n testns -l app=mongo -o jsonpath='{.items[0].metadata.name}')
MONGO_ROOT_USER="mongoadmin"
MONGO_ROOT_PASS="mongopassword"
# NOTE: DB names are formatted to match the dump script's logic (service-name -> service_name)
MONGO_DBS=("n_event_hub" "n_machine_maintenance")

# --- PostgreSQL Setup ---
echo "--- Setting up PostgreSQL ---"

# 1. Create Databases
echo "Creating PostgreSQL databases..."
for db in "${POSTGRES_DBS[@]}"; do
  echo "  - Creating database: $db"
  kubectl exec -n testns "$POSTGRES_POD" -- psql -U "$POSTGRES_ADMIN_USER" -d "$POSTGRES_ADMIN_DB" -c "CREATE DATABASE \"$db\";"
done

# 2. Create Tables in each database
echo "Creating PostgreSQL tables..."
for db in "${POSTGRES_DBS[@]}"; do
  echo "  - Configuring database: $db"
  PG_SQL_COMMANDS="
    CREATE TABLE employees (id SERIAL PRIMARY KEY, name VARCHAR(100), position VARCHAR(100));
    INSERT INTO employees (name, position) VALUES ('John Doe', 'Engineer'), ('Jane Smith', 'Manager');
    CREATE TABLE devices (id SERIAL PRIMARY KEY, name VARCHAR(100), status VARCHAR(100));
    INSERT INTO devices (name, status) VALUES ('Device A', 'online'), ('Device B', 'offline');
  "
  kubectl exec -n testns "$POSTGRES_POD" -- psql -U "$POSTGRES_ADMIN_USER" -d "$db" -c "$PG_SQL_COMMANDS"
done

echo "--- PostgreSQL setup complete ---"


# --- MongoDB Setup ---
echo "--- Setting up MongoDB ---"

MONGO_CONN_STRING="mongodb://${MONGO_ROOT_USER}:${MONGO_ROOT_PASS}@localhost:27017/?authSource=admin"

echo "Creating MongoDB databases and collections..."
for db in "${MONGO_DBS[@]}"; do
  echo "  - Creating database and collections in: $db"
  MONGO_JS_INSERT="
    db.getSiblingDB('$db').employees.insertMany([{ name: 'John Doe', position: 'Software Engineer' }, { name: 'Jane Smith', position: 'Project Manager' }]);
    db.getSiblingDB('$db').sensors.insertMany([{ type: 'temp', reading: 25 }, { type: 'humidity', reading: 60 }]);
  "
  kubectl exec -n testns "$MONGO_POD" -- mongo "$MONGO_CONN_STRING" --eval "$MONGO_JS_INSERT"
done

echo "--- MongoDB setup complete ---"
echo "--- All database setup is complete! ---"

```

### 3. How to Run the Setup

Make the script executable and run it:

```bash
chmod +x setup_test_env.sh
./setup_test_env.sh
```

---

## 4. Test Cases

Once the setup is complete, run the following commands from the `zl-db-dump` directory to test the dump script. Each command should result in a successful log output and a final pre-signed URL.

### PostgreSQL Tests

1.  **Test PG: Dump an entire database**
    *   This tests the backup of the `kalpha_n_event_hub` database.
    ```bash
    python3 zl_db_dump.py --env kalpha --cluster postgres --service n-event-hub
    ```

2.  **Test PG: Dump a specific table**
    *   This tests the backup of only the `devices` table from the `kalpha_n_machine_maintenance` database.
    ```bash
    python3 zl_db_dump.py --env kalpha --cluster postgres --service n-machine-maintenance --table devices
    ```

3.  **Test PG: Dump another specific database**
    *   This tests the backup of the `kalpha_n_authentication` database.
    ```bash
    python3 zl_db_dump.py --env kalpha --cluster postgres --service n-authentication
    ```

### MongoDB Tests

1.  **Test Mongo: Dump an entire database**
    *   This tests the backup of the entire `n_machine_maintenance` database.
    ```bash
    python3 zl_db_dump.py --env kalpha --cluster mongo --service n-machine-maintenance
    ```

2.  **Test Mongo: Dump a specific collection**
    *   This tests the backup of only the `sensors` collection from the `n_event_hub` database.
    ```bash
    python3 zl_db_dump.py --env kalpha --cluster mongo --service n-event-hub --collection sensors
    ```

3.  **Test Mongo: Full Cluster Dump**
    *   This tests the backup of the entire MongoDB cluster. Note that the `--service` flag is omitted.
    ```bash
    python3 zl_db_dump.py --env kalpha --cluster mongo
    ```

## 5. Verification

For each test case, monitor the console output. A successful test will show:
-   Logs for each step (command execution, compression, S3 upload).
-   No `ERROR` messages.
-   A final "Backup complete" message with a mock pre-signed URL.

