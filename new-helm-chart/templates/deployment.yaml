apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "python-app-chart.fullname" . }}
  labels:
    {{- include "python-app-chart.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "python-app-chart.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "python-app-chart.selectorLabels" . | nindent 8 }}
    spec:
      initContainers:
        - name: install-dependencies
          image: "{{ .Values.initImage.repository }}:{{ .Values.initImage.tag }}"
          command: ["sh", "-c", "pip install -r /app/requirements.txt -t /python-packages"]
          volumeMounts:
            - name: app-code
              mountPath: /app
            - name: python-packages
              mountPath: /python-packages
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          workingDir: /app
          command: ["gunicorn"]
          args:
            - "--bind"
            - "0.0.0.0:8080"
            - "app:app"
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - name: app-code
              mountPath: /app
            - name: python-packages
              mountPath: /python-packages
          env:
            - name: PYTHONPATH
              value: /python-packages
            - name: PATH
              value: /usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/python-packages/bin
      volumes:
        - name: app-code
          configMap:
            name: {{ include "python-app-chart.fullname" . }}-configmap
        - name: python-packages
          emptyDir: {}
