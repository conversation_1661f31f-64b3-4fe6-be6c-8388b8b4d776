replicaCount: 1

image:
  repository: python
  pullPolicy: IfNotPresent
  tag: "3.9-slim"

initImage:
  repository: python
  tag: "3.9-slim"

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: true
  className: "external-nginx"
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-issuer"
    nginx.ingress.kubernetes.io/limit-rpm: "2"                   
    nginx.ingress.kubernetes.io/limit-connections: "1"              
    nginx.ingress.kubernetes.io/limit-burst-multiplier: "2"           
    nginx.ingress.kubernetes.io/limit-req-status-code: "429"   
    nginx.ingress.kubernetes.io/limit-whitelist: "127.0.0.1"

  hosts:
    - host: nginx-demo.strawmine.com
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls:
    - hosts:
        - nginx-demo.strawmine.com
      secretName: nginx-demo.strawmine.com-tls

resources: {}

configMap:
  enabled: true
