# Kafka UI OAuth2 Secret Management Guide

This guide explains how to securely manage Google OAuth2 credentials for Kafka UI using Kubernetes secrets instead of storing them in plain text in your values files.

## Files Overview

1. **`oauth2-secret.yaml`** - Kubernetes secret manifest containing OAuth2 credentials
2. **`custom-values-with-secret.yaml`** - Updated Helm values file that references the secret
3. **`custom-values.yaml`** - Original file (keep for reference, but don't use in production)

## Step-by-Step Implementation

### Step 1: Create the Kubernetes Secret

You have two options for creating the secret:

#### Option A: Using the manifest file (Recommended for GitOps)
```bash
# Apply the secret manifest
kubectl apply -f oauth2-secret.yaml

# Verify the secret was created
kubectl get secret kafka-ui-oauth2-secret
kubectl describe secret kafka-ui-oauth2-secret
```

#### Option B: Using kubectl command (Recommended for manual deployment)
```bash
# Create secret directly with kubectl
kubectl create secret generic kafka-ui-oauth2-secret \
  --from-literal=client-id="867203584040-j7pfakbprggkdn8b8iseglm7ivpbqcuo.apps.googleusercontent.com" \
  --from-literal=client-secret="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" \
  --namespace=default

# Add labels for better organization
kubectl label secret kafka-ui-oauth2-secret \
  app.kubernetes.io/name=kafka-ui \
  app.kubernetes.io/component=oauth2-credentials
```

### Step 2: Deploy Kafka UI with Secret-based Configuration

```bash
# Deploy using the updated values file
helm install kafka-ui . -f custom-values-with-secret.yaml

# Or upgrade existing deployment
helm upgrade kafka-ui . -f custom-values-with-secret.yaml
```

### Step 3: Verify the Deployment

```bash
# Check if pods are running
kubectl get pods -l app.kubernetes.io/name=kafka-ui

# Check environment variables are loaded from secret
kubectl describe pod -l app.kubernetes.io/name=kafka-ui | grep -A 10 "Environment:"

# Check logs for OAuth2 configuration
kubectl logs -l app.kubernetes.io/name=kafka-ui | grep -i oauth
```

## Configuration Explanation

### Secret Structure

The secret contains two keys:
- `client-id`: Google OAuth2 application client ID
- `client-secret`: Google OAuth2 application client secret

### Environment Variable Mapping

In `custom-values-with-secret.yaml`, the secret values are mapped to environment variables:

```yaml
envs:
  secretMappings:
    # Maps to Spring Boot OAuth2 client ID property
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_GOOGLE_CLIENT_ID:
      name: kafka-ui-oauth2-secret
      keyName: client-id
    
    # Maps to Spring Boot OAuth2 client secret property
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_GOOGLE_CLIENT_SECRET:
      name: kafka-ui-oauth2-secret
      keyName: client-secret
```

### How It Works

1. Kubernetes creates environment variables from the secret
2. Kafka UI reads these environment variables at startup
3. Spring Boot OAuth2 configuration uses these values automatically
4. The OAuth2 configuration in `yamlApplicationConfig` no longer needs inline credentials

## Security Benefits

### ✅ Improved Security
- Credentials are not stored in version control
- Secret values are base64 encoded in etcd
- Can be encrypted at rest with Kubernetes encryption
- Access controlled via RBAC

### ✅ Better Secret Management
- Centralized credential storage
- Easy rotation without changing Helm charts
- Audit trail for secret access
- Integration with external secret management systems

## Secret Rotation

### Manual Rotation
```bash
# Update the secret with new credentials
kubectl patch secret kafka-ui-oauth2-secret -p='{"data":{"client-secret":"<new-base64-encoded-secret>"}}'

# Restart pods to pick up new secret
kubectl rollout restart deployment kafka-ui
```

### Automated Rotation (Advanced)
Consider using tools like:
- **External Secrets Operator** - Sync from external secret stores
- **Sealed Secrets** - Encrypt secrets for GitOps workflows
- **Vault Agent** - Integrate with HashiCorp Vault

## Troubleshooting

### Common Issues

1. **Secret Not Found**
   ```bash
   # Check if secret exists in correct namespace
   kubectl get secrets -n <namespace>
   
   # Verify secret content
   kubectl get secret kafka-ui-oauth2-secret -o yaml
   ```

2. **Environment Variables Not Set**
   ```bash
   # Check pod environment variables
   kubectl exec -it <kafka-ui-pod> -- env | grep SPRING_SECURITY_OAUTH2
   ```

3. **OAuth2 Authentication Fails**
   ```bash
   # Check application logs
   kubectl logs -l app.kubernetes.io/name=kafka-ui | grep -i "oauth\|auth"
   ```

### Validation Commands

```bash
# Decode and verify secret values
kubectl get secret kafka-ui-oauth2-secret -o jsonpath='{.data.client-id}' | base64 -d
kubectl get secret kafka-ui-oauth2-secret -o jsonpath='{.data.client-secret}' | base64 -d

# Check if environment variables are properly mapped
kubectl describe pod -l app.kubernetes.io/name=kafka-ui | grep -A 20 "Environment:"
```

## Migration from Plain Text Configuration

### Before (Insecure)
```yaml
# In custom-values.yaml
auth:
  type: OAUTH2
  oauth2:
    client:
      google:
        clientId: 867203584040-j7pfakbprggkdn8b8iseglm7ivpbqcuo.apps.googleusercontent.com
        clientSecret: 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
```

### After (Secure)
```yaml
# In custom-values-with-secret.yaml
auth:
  type: OAUTH2
  oauth2:
    client:
      google:
        # Credentials loaded from environment variables
        # No inline secrets!

envs:
  secretMappings:
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_GOOGLE_CLIENT_ID:
      name: kafka-ui-oauth2-secret
      keyName: client-id
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_GOOGLE_CLIENT_SECRET:
      name: kafka-ui-oauth2-secret
      keyName: client-secret
```

## Best Practices

### ✅ Do's
- Use Kubernetes secrets for all sensitive data
- Implement secret rotation procedures
- Use namespace isolation
- Add appropriate labels and annotations
- Monitor secret access with audit logs

### ❌ Don'ts
- Don't commit secrets to version control
- Don't use plain text in Helm values
- Don't share secrets across environments
- Don't forget to rotate credentials regularly

## Next Steps

1. **Implement the secret-based configuration**
2. **Remove the original `custom-values.yaml` from production use**
3. **Set up secret rotation procedures**
4. **Consider implementing external secret management**
5. **Add monitoring for authentication failures**

This approach significantly improves the security posture of your Kafka UI deployment while maintaining the same functionality.
