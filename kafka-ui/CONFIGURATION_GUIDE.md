# Kafka UI Configuration Guide

This document provides comprehensive documentation for the Kafka UI Helm chart configuration, comparing the default `values.yaml` with the custom `custom-values.yaml` configuration.

## Overview

The Kafka UI is deployed using a Helm chart with two configuration files:
- `values.yaml`: Default configuration template with all available options
- `custom-values.yaml`: Production-ready customized configuration

## Configuration Comparison

### Application Configuration

#### Kafka Cluster Connection
Both configurations connect to the same Kafka cluster:

```yaml
yamlApplicationConfig:
  kafka:
    clusters:
      - name: confluent-4.1.1
        bootstrapServers: ***********:9093,***********:9093,************:9093
```

**Key Points:**
- Cluster name: `confluent-4.1.1`
- Bootstrap servers: Three Kafka brokers on port 9093
- Uses IP addresses for direct connection

### Authentication & Authorization

#### Default Configuration (values.yaml)
- Authentication is **disabled** by default
- Contains commented examples for:
  - `LOGIN_FORM` authentication
  - Basic username/password setup
  - OAuth2 configuration

#### Custom Configuration (custom-values.yaml)
Implements **Google OAuth2** authentication with RBAC:

```yaml
auth:
  type: OAUTH2
  oauth2:
    client:
      google:
        provider: google
        clientId: 867203584040-j7pfakbprggkdn8b8iseglm7ivpbqcuo.apps.googleusercontent.com
        clientSecret: 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
        user-name-attribute: email
        custom-params:
          type: google
          allowedDomain: solvei8.com
```

**Security Features:**
- Domain restriction to `solvei8.com`
- Email-based user identification
- Integration with Google OAuth2

### Role-Based Access Control (RBAC)

The custom configuration defines two roles:

#### 1. Admin Role
**Users with full access:**
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>

**Permissions:**
- All operations on topics, consumers, schemas, and connectors
- KSQL execution rights
- ACL and configuration management

#### 2. Read-Only Role
**Users:**
- <EMAIL>
- All users from solvei8.com domain (fallback)

**Permissions:**
- View topics and read messages
- View consumers, schemas, and connectors
- View ACLs and cluster configuration
- No modification rights

### Resource Management

#### Default Configuration
```yaml
resources: {}  # No limits set
```

#### Custom Configuration
```yaml
resources:
  limits:
    cpu: 300m
    memory: 712Mi
  requests:
    cpu: 200m
    memory: 512Mi
```

**Resource Allocation:**
- CPU: 200m requested, 300m limit
- Memory: 512Mi requested, 712Mi limit
- Suitable for production workloads

### Network Exposure

#### Default Configuration
```yaml
ingress:
  enabled: false
  # Basic ingress template provided
```

#### Custom Configuration
```yaml
ingress:
  enabled: true
  annotations: 
    cert-manager.io/cluster-issuer: letsencrypt-issuer
  ingressClassName: "external-nginx"
  path: "/"
  host: "kafka-ui.strawmine.com"
  tls:
    enabled: true
    secretName: "kafka-ui.strawmine.com-tls"
```

**Production Features:**
- External NGINX ingress controller
- Automatic SSL certificate management via cert-manager
- Let's Encrypt SSL certificates
- Custom domain: `kafka-ui.strawmine.com`

## Deployment Differences

| Feature | Default (values.yaml) | Custom (custom-values.yaml) |
|---------|----------------------|----------------------------|
| Authentication | Disabled | Google OAuth2 |
| Authorization | None | RBAC with 2 roles |
| Resource Limits | None | CPU: 300m, Memory: 712Mi |
| Ingress | Disabled | Enabled with SSL |
| Domain | None | kafka-ui.strawmine.com |
| SSL/TLS | Not configured | Let's Encrypt certificates |

## Security Considerations

### Secrets Management
⚠️ **Important**: The custom configuration contains sensitive information:
- Google OAuth2 client secret
- Should be moved to Kubernetes secrets in production

### Recommended Security Improvements
1. Move OAuth2 credentials to Kubernetes secrets
2. Use `existingSecret` parameter instead of inline secrets
3. Implement network policies for additional security
4. Consider using service mesh for internal communication

## Usage Instructions

### Deploy with Default Configuration
```bash
helm install kafka-ui . -f values.yaml
```

### Deploy with Custom Configuration
```bash
helm install kafka-ui . -f custom-values.yaml
```

### Override Specific Values
```bash
helm install kafka-ui . -f values.yaml --set ingress.enabled=true
```

## Maintenance Notes

- Monitor resource usage and adjust limits as needed
- Regularly rotate OAuth2 credentials
- Keep SSL certificates updated (automated with cert-manager)
- Review RBAC permissions periodically
- Update allowed domain list as team changes
