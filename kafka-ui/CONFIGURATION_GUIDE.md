# Kafka UI Custom Configuration Documentation

This document provides comprehensive documentation for the Kafka UI custom configuration defined in `custom-values.yaml`.

## Overview

The `custom-values.yaml` file contains a production-ready configuration for Kafka UI with Google OAuth2 authentication, role-based access control (RBAC), resource limits, and SSL-enabled ingress.

## Configuration Breakdown

### 1. Kafka Cluster Connection

```yaml
yamlApplicationConfig:
  kafka:
    clusters:
      - name: confluent-4.1.1
        bootstrapServers: 10.0.136.28:9093,10.0.149.84:9093,10.0.152.181:9093
```

**Configuration Details:**
- **Cluster Name**: `confluent-4.1.1`
- **Bootstrap Servers**: Three Kafka brokers
  - `10.0.136.28:9093`
  - `10.0.149.84:9093`
  - `10.0.152.181:9093`
- **Port**: 9093 (typically used for SSL/SASL connections)

### 2. Authentication Configuration

The configuration implements Google OAuth2 authentication:

```yaml
auth:
  type: OAUTH2
  oauth2:
    client:
      google:
        provider: google
        clientId: 867203584040-j7pfakbprggkdn8b8iseglm7ivpbqcuo.apps.googleusercontent.com
        clientSecret: 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
        user-name-attribute: email
        custom-params:
          type: google
          allowedDomain: solvei8.com
```

**Authentication Features:**
- **Provider**: Google OAuth2
- **User Identification**: Email addresses
- **Domain Restriction**: Only users from `solvei8.com` domain
- **Client ID**: Google OAuth2 application client ID
- **Client Secret**: Google OAuth2 application secret

### 3. Role-Based Access Control (RBAC)

The configuration defines two distinct roles with different permission levels:

#### Admin Role

**Authorized Users:**
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>

**Admin Permissions:**
- **Topics**: Full access (all actions on all topics)
- **Consumers**: Full access (all actions on all consumer groups)
- **Schemas**: Full access (all actions on all schemas)
- **Connectors**: Full access (all actions on all connectors)
- **KSQL**: Execute queries
- **ACLs**: View and edit access control lists
- **Application Config**: View and edit application configuration
- **Cluster Config**: View and edit cluster configuration

#### Read-Only Role

**Authorized Users:**
- <EMAIL>
- All users from solvei8.com domain (fallback rule)

**Read-Only Permissions:**
- **Topics**: View, read messages, and view analysis
- **Consumers**: View consumer groups only
- **Schemas**: View schemas only
- **Connectors**: View connectors only
- **ACLs**: View access control lists only
- **Cluster Config**: View cluster configuration only

### 4. Resource Management

```yaml
resources:
  limits:
    cpu: 300m
    memory: 712Mi
  requests:
    cpu: 200m
    memory: 512Mi
```

**Resource Allocation:**
- **CPU Request**: 200 millicores (0.2 CPU cores)
- **CPU Limit**: 300 millicores (0.3 CPU cores)
- **Memory Request**: 512 MiB
- **Memory Limit**: 712 MiB

**Sizing Rationale:**
- Suitable for medium-scale Kafka clusters
- Provides headroom for peak usage
- Prevents resource starvation of other pods

### 5. Network Exposure & SSL Configuration

```yaml
ingress:
  enabled: true
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-issuer
  ingressClassName: "external-nginx"
  path: "/"
  host: "kafka-ui.strawmine.com"
  tls:
    enabled: true
    secretName: "kafka-ui.strawmine.com-tls"
```

**Ingress Configuration:**
- **Enabled**: Yes
- **Ingress Class**: `external-nginx` (external NGINX ingress controller)
- **Domain**: `kafka-ui.strawmine.com`
- **Path**: `/` (root path)
- **SSL/TLS**: Enabled with Let's Encrypt certificates
- **Certificate Secret**: `kafka-ui.strawmine.com-tls`
- **Cert-Manager**: Automatic certificate management

## Security Features

### 1. Authentication Security
- OAuth2 integration with Google
- Domain-based access restriction
- Email-based user identification

### 2. Authorization Security
- Granular RBAC permissions
- Principle of least privilege
- Separate admin and read-only roles

### 3. Network Security
- SSL/TLS encryption for web traffic
- Automatic certificate management
- External ingress controller isolation

## Deployment Instructions

### Deploy Kafka UI with Custom Configuration
```bash
helm install kafka-ui . -f custom-values.yaml
```

### Upgrade Existing Deployment
```bash
helm upgrade kafka-ui . -f custom-values.yaml
```

### Verify Deployment
```bash
kubectl get pods -l app.kubernetes.io/name=kafka-ui
kubectl get ingress
kubectl get secrets kafka-ui.strawmine.com-tls
```

## Security Recommendations

### ⚠️ Critical Security Issues

1. **OAuth2 Credentials Exposure**
   - Client secret is stored in plain text
   - **Recommendation**: Move to Kubernetes secrets

2. **Improved Secret Management**
   ```yaml
   # Use existing secret instead
   existingSecret: "kafka-ui-oauth-secrets"

   # Remove inline credentials
   # clientSecret: 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
   ```

### Best Practices

1. **Credential Rotation**
   - Rotate OAuth2 credentials regularly
   - Update Google OAuth2 application settings

2. **Access Review**
   - Periodically review user access lists
   - Remove inactive users from admin role

3. **Monitoring**
   - Monitor resource usage
   - Set up alerts for authentication failures
   - Track user access patterns

## Troubleshooting

### Common Issues

1. **OAuth2 Authentication Failures**
   - Verify Google OAuth2 application configuration
   - Check domain restrictions
   - Validate client ID and secret

2. **SSL Certificate Issues**
   - Ensure cert-manager is installed and configured
   - Check Let's Encrypt rate limits
   - Verify DNS resolution for the domain

3. **Resource Constraints**
   - Monitor pod resource usage
   - Adjust limits if needed
   - Check for memory leaks

### Useful Commands

```bash
# Check pod logs
kubectl logs -l app.kubernetes.io/name=kafka-ui

# Check ingress status
kubectl describe ingress kafka-ui

# Check certificate status
kubectl describe certificate kafka-ui.strawmine.com-tls

# Check resource usage
kubectl top pods -l app.kubernetes.io/name=kafka-ui
```

## Maintenance Tasks

### Regular Maintenance
- [ ] Review and update user access lists monthly
- [ ] Monitor resource usage and adjust limits quarterly
- [ ] Rotate OAuth2 credentials every 6 months
- [ ] Review and update RBAC permissions annually

### Security Audits
- [ ] Audit user access logs
- [ ] Review authentication patterns
- [ ] Validate SSL certificate expiration
- [ ] Check for security updates
