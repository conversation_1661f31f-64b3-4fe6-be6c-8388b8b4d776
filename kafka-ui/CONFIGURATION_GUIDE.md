# Kafka UI Custom Configuration Documentation

This document provides comprehensive documentation for the Kafka UI custom configuration defined in `custom-values.yaml`.

## Overview

The `custom-values.yaml` file contains a production-ready configuration for Kafka UI with:
- **Secure Google OAuth2 authentication** using Kubernetes secrets
- **Role-based access control (RBAC)** with admin and read-only roles
- **Resource limits** for production workloads
- **SSL-enabled ingress** with automatic certificate management

## Prerequisites

Before deploying, you must create the OAuth2 credentials secret:

```bash
kubectl create secret generic kafka-ui-oauth2-secret \
  --from-literal=OAUTH2_CLIENT_ID="867203584040-j7pfakbprggkdn8b8iseglm7ivpbqcuo.apps.googleusercontent.com" \
  --from-literal=OAUTH2_CLIENT_SECRET="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"
```

## Configuration Breakdown

### 1. Secret Reference

```yaml
existingSecret: "kafka-ui-oauth2-secret"
```

**Purpose**: References the Kubernetes secret containing OAuth2 credentials. This loads all environment variables from the secret into the Kafka UI pod.

### 2. Kafka Cluster Connection

```yaml
yamlApplicationConfig:
  kafka:
    clusters:
      - name: confluent-4.1.1
        bootstrapServers: 10.0.136.28:9093,10.0.149.84:9093,10.0.152.181:9093
```

**Configuration Details:**
- **Cluster Name**: `confluent-4.1.1`
- **Bootstrap Servers**: Three Kafka brokers
  - `10.0.136.28:9093`
  - `10.0.149.84:9093`
  - `10.0.152.181:9093`
- **Port**: 9093 (typically used for SSL/SASL connections)

### 3. Secure Authentication Configuration

The configuration implements Google OAuth2 authentication with secure credential management:

```yaml
auth:
  type: OAUTH2
  oauth2:
    client:
      google:
        provider: google
        clientId: ${OAUTH2_CLIENT_ID}        # ← Loaded from secret
        clientSecret: ${OAUTH2_CLIENT_SECRET} # ← Loaded from secret
        user-name-attribute: email
        custom-params:
          type: google
          allowedDomain: solvei8.com
```

**Authentication Features:**
- **Provider**: Google OAuth2
- **User Identification**: Email addresses
- **Domain Restriction**: Only users from `solvei8.com` domain
- **Secure Credentials**: OAuth2 credentials loaded from Kubernetes secret using variable substitution
- **Variable Substitution**: `${OAUTH2_CLIENT_ID}` and `${OAUTH2_CLIENT_SECRET}` are replaced with actual values at runtime

### 3. Role-Based Access Control (RBAC)

The configuration defines two distinct roles with different permission levels:

#### Admin Role

**Authorized Users:**
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>

**Admin Permissions:**
- **Topics**: Full access (all actions on all topics)
- **Consumers**: Full access (all actions on all consumer groups)
- **Schemas**: Full access (all actions on all schemas)
- **Connectors**: Full access (all actions on all connectors)
- **KSQL**: Execute queries
- **ACLs**: View and edit access control lists
- **Application Config**: View and edit application configuration
- **Cluster Config**: View and edit cluster configuration

#### Read-Only Role

**Authorized Users:**
- All users from solvei8.com domain (domain-wide access)

**Read-Only Permissions:**
- **Topics**: View, read messages, and view analysis
- **Consumers**: View consumer groups only
- **Schemas**: View schemas only
- **Connectors**: View connectors only
- **ACLs**: View access control lists only
- **Cluster Config**: View cluster configuration only

### 4. Resource Management

```yaml
resources:
  limits:
    cpu: 300m
    memory: 712Mi
  requests:
    cpu: 200m
    memory: 512Mi
```

**Resource Allocation:**
- **CPU Request**: 200 millicores (0.2 CPU cores)
- **CPU Limit**: 300 millicores (0.3 CPU cores)
- **Memory Request**: 512 MiB
- **Memory Limit**: 712 MiB

**Sizing Rationale:**
- Suitable for medium-scale Kafka clusters
- Provides headroom for peak usage
- Prevents resource starvation of other pods

### 5. Network Exposure & SSL Configuration

```yaml
ingress:
  enabled: true
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-issuer
  ingressClassName: "external-nginx"
  path: "/"
  host: "kafka-ui.strawmine.com"
  tls:
    enabled: true
    secretName: "kafka-ui.strawmine.com-tls"
```

**Ingress Configuration:**
- **Enabled**: Yes
- **Ingress Class**: `external-nginx` (external NGINX ingress controller)
- **Domain**: `kafka-ui.strawmine.com`
- **Path**: `/` (root path)
- **SSL/TLS**: Enabled with Let's Encrypt certificates
- **Certificate Secret**: `kafka-ui.strawmine.com-tls`
- **Cert-Manager**: Automatic certificate management

## Security Features

### 1. Authentication Security
- OAuth2 integration with Google
- Domain-based access restriction
- Email-based user identification

### 2. Authorization Security
- Granular RBAC permissions
- Principle of least privilege
- Separate admin and read-only roles

### 3. Network Security
- SSL/TLS encryption for web traffic
- Automatic certificate management
- External ingress controller isolation

## Deployment Instructions

### Step 1: Create OAuth2 Secret
```bash
# Create the secret with OAuth2 credentials
kubectl create secret generic kafka-ui-oauth2-secret \
  --from-literal=OAUTH2_CLIENT_ID="867203584040-j7pfakbprggkdn8b8iseglm7ivpbqcuo.apps.googleusercontent.com" \
  --from-literal=OAUTH2_CLIENT_SECRET="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"

# Verify secret creation
kubectl get secret kafka-ui-oauth2-secret
```

### Step 2: Deploy Kafka UI
```bash
# Deploy with custom configuration
helm install kafka-ui . -f custom-values.yaml

# Or upgrade existing deployment
helm upgrade kafka-ui . -f custom-values.yaml
```

### Step 3: Verify Deployment
```bash
# Check pods are running
kubectl get pods -l app.kubernetes.io/name=kafka-ui

# Check ingress is created
kubectl get ingress

# Check SSL certificate
kubectl get secrets kafka-ui.strawmine.com-tls

# Verify OAuth2 environment variables are loaded
kubectl exec -it <kafka-ui-pod> -- env | grep OAUTH2
```

## Security Features

### ✅ Current Security Implementation

1. **Secure OAuth2 Credentials**
   - OAuth2 credentials are stored in Kubernetes secrets
   - No plain text secrets in configuration files
   - Variable substitution used for credential injection

2. **Secret Management**
   ```yaml
   # Current secure implementation
   existingSecret: "kafka-ui-oauth2-secret"

   # Variables used in configuration
   clientId: ${OAUTH2_CLIENT_ID}
   clientSecret: ${OAUTH2_CLIENT_SECRET}
   ```

### Best Practices

1. **Credential Rotation**
   - Rotate OAuth2 credentials regularly
   - Update Google OAuth2 application settings

2. **Access Review**
   - Periodically review user access lists
   - Remove inactive users from admin role

3. **Monitoring**
   - Monitor resource usage
   - Set up alerts for authentication failures
   - Track user access patterns

## Troubleshooting

### Common Issues

1. **OAuth2 Authentication Failures**
   - Verify Google OAuth2 application configuration
   - Check domain restrictions
   - Validate client ID and secret

2. **SSL Certificate Issues**
   - Ensure cert-manager is installed and configured
   - Check Let's Encrypt rate limits
   - Verify DNS resolution for the domain

3. **Resource Constraints**
   - Monitor pod resource usage
   - Adjust limits if needed
   - Check for memory leaks

### Useful Commands

```bash
# Check pod logs
kubectl logs -l app.kubernetes.io/name=kafka-ui

# Check ingress status
kubectl describe ingress kafka-ui

# Check certificate status
kubectl describe certificate kafka-ui.strawmine.com-tls

# Check resource usage
kubectl top pods -l app.kubernetes.io/name=kafka-ui
```

## Maintenance Tasks

### Regular Maintenance
- [ ] Review and update user access lists monthly
- [ ] Monitor resource usage and adjust limits quarterly
- [ ] Rotate OAuth2 credentials every 6 months
- [ ] Review and update RBAC permissions annually

### Security Audits
- [ ] Audit user access logs
- [ ] Review authentication patterns
- [ ] Validate SSL certificate expiration
- [ ] Check for security updates

## Final Configuration Summary

Your `custom-values.yaml` implements a secure, production-ready Kafka UI deployment with:

### Security
- ✅ **OAuth2 credentials in Kubernetes secrets** (not in config files)
- ✅ **Variable substitution** using `${OAUTH2_CLIENT_ID}` and `${OAUTH2_CLIENT_SECRET}`
- ✅ **Domain-restricted authentication** (solvei8.com only)
- ✅ **SSL/TLS encryption** with automatic certificate management

### Access Control
- ✅ **7 named admin users** with full Kafka management permissions
- ✅ **Domain-wide read-only access** for all solvei8.com users
- ✅ **Granular RBAC permissions** for topics, consumers, schemas, connectors

### Infrastructure
- ✅ **Production resource limits** (300m CPU, 712Mi memory)
- ✅ **External ingress** with NGINX controller
- ✅ **Custom domain** (kafka-ui.strawmine.com)
- ✅ **Let's Encrypt SSL certificates**

### Deployment
- ✅ **Simple secret creation** with kubectl command
- ✅ **Standard Helm deployment** process
- ✅ **Easy credential rotation** without config changes

This configuration provides enterprise-grade security and functionality for your Kafka UI deployment.
