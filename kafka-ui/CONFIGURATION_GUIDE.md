# Kafka UI Setup - What I Did and Why

Hey team! Here's how I set up our Kafka UI and why I made these choices. Let me walk you through it.

## The Problem

We needed a way to manage our Kafka cluster without everyone SSHing into servers or sharing passwords. Plus, not everyone needs full admin access - developers just need to see their topics and messages.

## What I Built

A secure Kafka UI that:
- Uses our Google accounts for login (no new passwords!)
- Gives admins full access, everyone else read-only
- Has a proper domain with SSL certificates
- Runs reliably in our Kubernetes cluster

## 1. Authentication with Google

Instead of creating yet another username/password system, I used our existing Google Workspace. Here's why:

- We already have Google accounts
- No new passwords to remember or manage
- Only people with `@solvei8.com` emails can access
- When someone leaves the company, they automatically lose access

```yaml
# I store the OAuth2 credentials in a Kubernetes secret (not in the config file)
existingSecret: "kafka-ui-oauth2-secret"

yamlApplicationConfig:
  auth:
    type: OAUTH2
    oauth2:
      client:
        google:
          provider: google
          clientId: ${OAUTH2_CLIENT_ID}        # Comes from the secret
          clientSecret: ${OAUTH2_CLIENT_SECRET} # Comes from the secret
          allowedDomain: solvei8.com           # Only our company emails
```

**To set this up, first create the secret:**
```bash
kubectl create secret generic kafka-ui-oauth2-secret \
  --from-literal=OAUTH2_CLIENT_ID="************-j7pfakbprggkdn8b8iseglm7ivpbqcuo.apps.googleusercontent.com" \
  --from-literal=OAUTH2_CLIENT_SECRET="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" \
  -n testns
```

## 2. Kafka Connection

I connected directly to our three Kafka brokers using their IP addresses:

```yaml
yamlApplicationConfig:
  kafka:
    clusters:
      - name: confluent-4.1.1
        bootstrapServers: ***********:9093,***********:9093,************:9093
```

**Why I did this:**
- **Three IPs**: If one broker goes down, Kafka UI can still connect to the others
- **Port 9093**: This is our secure port (not the plain 9092)
- **Direct IPs**: More reliable than DNS in our setup
- **confluent-4.1.1**: Matches what we call this cluster internally

## 3. User Permissions

I set up two levels of access because not everyone needs to be able to delete topics!

**Admins (7 specific people):**
```yaml
rbac:
  roles:
    - name: "admin"
      subjects:
        - provider: oauth_google
          type: user
          value: "<EMAIL>"    # Our platform team
        - provider: oauth_google
          type: user
          value: "<EMAIL>"
        # ... 5 more platform team members
      permissions:
        - resource: topic
          actions: [ "all" ]                   # Can do everything
```

**Everyone Else (read-only):**
```yaml
- name: "read-only"
  subjects:
    - provider: oauth_google
      type: domain
      value: "solvei8.com"                    # All company emails
  permissions:
    - resource: topic
      actions: ["VIEW", "MESSAGES_READ"]      # Can see but not change
    - resource: consumer
      actions: [ "view" ]                     # Can see consumer lag
```

**Why I did it this way:**
- Platform team needs full access to manage and troubleshoot
- Developers can see their topics and debug issues themselves
- Nobody can accidentally delete important topics
- New team members automatically get read-only access

## 4. Resource Limits

I set specific limits so Kafka UI doesn't hog resources or get killed by Kubernetes:

```yaml
resources:
  requests:
    cpu: 200m        # Guaranteed minimum
    memory: 512Mi
  limits:
    cpu: 300m        # Maximum allowed
    memory: 712Mi
```

**Why these numbers:**
- **200m CPU**: Kafka UI normally uses about 150m, so this gives some buffer
- **300m limit**: Handles spikes when loading lots of topics
- **512Mi memory**: Enough for the Java app and caching
- **712Mi limit**: Room for large message payloads

This prevents Kafka UI from being slow (guaranteed resources) or crashing other apps (resource limits).

## 5. External Access

Instead of making everyone remember an IP address, I set up a proper domain with automatic SSL:

```yaml
ingress:
  enabled: true
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-issuer  # Auto SSL
  ingressClassName: "external-nginx"                    # External access
  host: "kafka-ui.strawmine.com"                       # Our domain
  tls:
    enabled: true                                       # Force HTTPS
```

**Why I chose this:**
- **Custom domain**: Easy to remember `kafka-ui.strawmine.com`
- **External access**: Works from home, coffee shops, anywhere
- **Free SSL**: Let's Encrypt gives us valid certificates
- **Auto-renewal**: Certificates update themselves, no manual work
- **No browser warnings**: Proper HTTPS, not self-signed certificates

Now everyone just goes to `https://kafka-ui.strawmine.com` and it works!

## How to Deploy This

**Step 1: Create the secret first**
```bash
kubectl create secret generic kafka-ui-oauth2-secret \
  --from-literal=OAUTH2_CLIENT_ID="************-j7pfakbprggkdn8b8iseglm7ivpbqcuo.apps.googleusercontent.com" \
  --from-literal=OAUTH2_CLIENT_SECRET="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" \
  -n testns
```

**Step 2: Deploy with Helm**
```bash
helm upgrade --install kafka-ui kafka-ui -n testns --values kafka-ui/custom-values.yaml
```

**Step 3: Check it worked**
```bash
# Check the pod is running
kubectl get pods -l app.kubernetes.io/name=kafka-ui -n testns

# Check you can access it
curl -I https://kafka-ui.strawmine.com
```

That's it! Go to `https://kafka-ui.strawmine.com` and log in with your Google account.

## What We Achieved: Security & Operational Benefits

### Security Wins for Our Team

**1. No More Shared Passwords**
- ✅ **Before**: Everyone shared a basic auth password
- ✅ **Now**: Each person uses their own Google account
- ✅ **Benefit**: Individual accountability and easier access management

**2. Secrets Management Done Right**
```yaml
# What we avoided (bad practice):
# clientSecret: "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"  # DON'T DO THIS

# What we implemented (secure):
existingSecret: "kafka-ui-oauth2-secret"  # Secret stored separately
clientId: ${OAUTH2_CLIENT_ID}             # Variable substitution
clientSecret: ${OAUTH2_CLIENT_SECRET}     # No hardcoded secrets
```

**3. Domain-Level Security**
- ✅ **Only `@solvei8.com` emails** can access
- ✅ **Automatic access** for new team members
- ✅ **Automatic revocation** when someone leaves (Google Workspace handles this)

### Operational Benefits

**1. Self-Service for Developers**
- ✅ **Read-only access** for all developers
- ✅ **Can debug their own topics** without asking platform team
- ✅ **Reduces support tickets** and speeds up troubleshooting

**2. Controlled Admin Access**
- ✅ **Only 7 platform team members** have admin rights
- ✅ **Clear accountability** - we know who can make changes
- ✅ **Easy to add/remove** admin users by updating the config

**3. Production-Ready Infrastructure**
- ✅ **Automatic SSL certificates** - no manual renewal needed
- ✅ **Professional domain** - easy for team to remember and access
- ✅ **Resource limits** - won't impact other applications

## When Things Go Wrong: Troubleshooting Guide

### Most Common Issues (And Why They Happen)

**1. "Can't Log In" - OAuth2 Authentication Failures**

**Why This Happens**:
- Google OAuth2 app configuration changed
- User's email not in allowed domain
- Client ID/secret mismatch

**How to Fix**:
```bash
# Check if OAuth2 variables are loaded correctly
kubectl exec -it <kafka-ui-pod> -n testns -- env | grep OAUTH2

# Check pod logs for auth errors
kubectl logs -l app.kubernetes.io/name=kafka-ui -n testns | grep -i oauth

# Verify secret exists and has correct values
kubectl get secret kafka-ui-oauth2-secret -n testns -o yaml
```

**2. "Site Not Secure" - SSL Certificate Issues**

**Why This Happens**:
- cert-manager not working
- DNS not pointing to ingress
- Let's Encrypt rate limits hit

**How to Fix**:
```bash
# Check certificate status
kubectl get certificate -n testns
kubectl describe certificate kafka-ui.strawmine.com-tls -n testns

# Check cert-manager logs
kubectl logs -n cert-manager deployment/cert-manager

# Verify DNS resolution
nslookup kafka-ui.strawmine.com
```

**3. "Page Won't Load" - Pod or Ingress Issues**

**Why This Happens**:
- Pod crashed due to resource limits
- Ingress controller not working
- Network connectivity issues

**How to Fix**:
```bash
# Check pod status first
kubectl get pods -l app.kubernetes.io/name=kafka-ui -n testns

# If pod is failing, check logs
kubectl logs -l app.kubernetes.io/name=kafka-ui -n testns

# Check ingress configuration
kubectl describe ingress kafka-ui -n testns

# Check resource usage
kubectl top pods -l app.kubernetes.io/name=kafka-ui -n testns
```

### Quick Diagnostic Commands

```bash
# Complete health check (run these in order)
helm status kafka-ui -n testns                    # Overall deployment status
kubectl get pods -l app.kubernetes.io/name=kafka-ui -n testns  # Pod health
kubectl get ingress -n testns                     # Ingress status
kubectl get certificate -n testns                 # SSL certificate status
kubectl logs -l app.kubernetes.io/name=kafka-ui -n testns --tail=50  # Recent logs
```

## Maintenance Tasks

### Regular Maintenance
- [ ] Review and update user access lists monthly
- [ ] Monitor resource usage and adjust limits quarterly
- [ ] Rotate OAuth2 credentials every 6 months
- [ ] Review and update RBAC permissions annually

### Security Audits
- [ ] Audit user access logs
- [ ] Review authentication patterns
- [ ] Validate SSL certificate expiration
- [ ] Check for security updates

## Final Configuration Summary

Your `custom-values.yaml` implements a secure, production-ready Kafka UI deployment with:

### Security
- ✅ **OAuth2 credentials in Kubernetes secrets** (not in config files)
- ✅ **Variable substitution** using `${OAUTH2_CLIENT_ID}` and `${OAUTH2_CLIENT_SECRET}`
- ✅ **Domain-restricted authentication** (solvei8.com only)
- ✅ **SSL/TLS encryption** with automatic certificate management

### Access Control
- ✅ **7 named admin users** with full Kafka management permissions
- ✅ **Domain-wide read-only access** for all solvei8.com users
- ✅ **Granular RBAC permissions** for topics, consumers, schemas, connectors

### Infrastructure
- ✅ **Production resource limits** (300m CPU, 712Mi memory)
- ✅ **External ingress** with NGINX controller
- ✅ **Custom domain** (kafka-ui.strawmine.com)
- ✅ **Let's Encrypt SSL certificates**

### Deployment
- ✅ **Simple secret creation** with kubectl command
- ✅ **Standard Helm deployment** process
- ✅ **Easy credential rotation** without config changes

This configuration provides enterprise-grade security and functionality for your Kafka UI deployment.
