# Kafka UI Setup Guide

## Overview

Kafka UI is a web-based tool for managing and monitoring Apache Kafka clusters. This document describes the configuration options available in the `custom-values.yaml` file for deploying Kafka UI with Helm.


## Configuration

### Authentication

Kafka UI supports OAuth2 authentication with <PERSON> as the identity provider.

#### Creating OAuth2 Secret

Before deploying Kafka UI, create a Kubernetes Secret containing OAuth2 credentials:

```bash
kubectl create secret generic kafka-ui-oauth2-secret \
  --from-literal=OAUTH2_CLIENT_ID="your-client-id" \
  --from-literal=OAUTH2_CLIENT_SECRET="your-client-secret" \
  --namespace=your-namespace
```

#### OAuth2 Configuration

Configure OAuth2 authentication in your values file:

```yaml
existingSecret: "kafka-ui-oauth2-secret"

yamlApplicationConfig:
  auth:
    type: OAUTH2
    oauth2:
      client:
        google:
          provider: google
          clientId: ${OAUTH2_CLIENT_ID}
          clientSecret: ${OAUTH2_CLIENT_SECRET}
          user-name-attribute: email
          custom-params:
            type: google
            allowedDomain: example.com
```

### Kafka Cluster Configuration

Configure Kafka cluster connections:

```yaml
yamlApplicationConfig:
  kafka:
    clusters:
      - name: confluent-4.1.1
        bootstrapServers: ***********:9093,***********:9093,************:9093
```

| Parameter | Description | Required |
|-----------|-------------|----------|
| `name` | Unique identifier for the Kafka cluster | Yes |
| `bootstrapServers` | Comma-separated list of Kafka broker addresses | Yes |

### Role-Based Access Control (RBAC)

Kafka UI supports fine-grained access control through RBAC configuration.

#### Roles

Define roles with specific permissions:

```yaml
yamlApplicationConfig:
  rbac:
    roles:
      - name: "admin"
        clusters:
          - confluent-4.1.1
        subjects:
          - provider: oauth_google
            type: user
            value: "<EMAIL>"
        permissions:
          - resource: topic
            value: ".*"
            actions: ["all"]
```

#### Permissions

| Resource | Actions | Description |
|----------|---------|-------------|
| `topic` | `VIEW`, `CREATE`, `EDIT`, `DELETE`, `MESSAGES_READ`, `all` | Topic management |
| `consumer` | `VIEW`, `EDIT`, `DELETE`, `RESET_OFFSETS`, `all` | Consumer group management |
| `schema` | `VIEW`, `CREATE`, `EDIT`, `DELETE`, `all` | Schema registry operations |
| `connect` | `VIEW`, `CREATE`, `EDIT`, `DELETE`, `all` | Kafka Connect operations |
| `ksql` | `execute` | KSQL query execution |
| `acl` | `view`, `edit` | Access control list management |
| `applicationconfig` | `view`, `edit` | Application configuration |
| `clusterconfig` | `view`, `edit` | Cluster configuration |

### Resource Management

Configure resource requests and limits for the Kafka UI pod:

```yaml
resources:
  limits:
    cpu: 300m
    memory: 712Mi
  requests:
    cpu: 200m
    memory: 512Mi
```

| Parameter | Description | Default |
|-----------|-------------|---------|
| `resources.requests.cpu` | CPU resource request | `200m` |
| `resources.requests.memory` | Memory resource request | `512Mi` |
| `resources.limits.cpu` | CPU resource limit | `300m` |
| `resources.limits.memory` | Memory resource limit | `712Mi` |

### Ingress Configuration

Enable external access to Kafka UI through an Ingress resource:

```yaml
ingress:
  enabled: true
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-issuer
  ingressClassName: "external-nginx"
  path: "/"
  host: "kafka-ui.example.com"
  tls:
    enabled: true
    secretName: "kafka-ui-tls"
```

### 4. Resource Management

```yaml
resources:
  limits:
    cpu: 300m
    memory: 712Mi
  requests:
    cpu: 200m
    memory: 512Mi
```

**Resource Allocation:**
- **CPU Request**: 200 millicores (0.2 CPU cores)
- **CPU Limit**: 300 millicores (0.3 CPU cores)
- **Memory Request**: 512 MiB
- **Memory Limit**: 712 MiB

**Sizing Rationale:**
- Suitable for medium-scale Kafka clusters
- Provides headroom for peak usage
- Prevents resource starvation of other pods

### 5. Network Exposure & SSL Configuration

```yaml
ingress:
  enabled: true
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-issuer
  ingressClassName: "external-nginx"
  path: "/"
  host: "kafka-ui.strawmine.com"
  tls:
    enabled: true
    secretName: "kafka-ui.strawmine.com-tls"
```

**Ingress Configuration:**
- **Enabled**: Yes
- **Ingress Class**: `external-nginx` (external NGINX ingress controller)
- **Domain**: `kafka-ui.strawmine.com`
- **Path**: `/` (root path)
- **SSL/TLS**: Enabled with Let's Encrypt certificates
- **Certificate Secret**: `kafka-ui.strawmine.com-tls`
- **Cert-Manager**: Automatic certificate management

## Security Features

### 1. Authentication Security
- OAuth2 integration with Google
- Domain-based access restriction
- Email-based user identification

### 2. Authorization Security
- Granular RBAC permissions
- Principle of least privilege
- Separate admin and read-only roles

### 3. Network Security
- SSL/TLS encryption for web traffic
- Automatic certificate management
- External ingress controller isolation

## Deployment Instructions

### Step 1: Create OAuth2 Secret
```bash
# Create the secret with OAuth2 credentials in the target namespace
kubectl create secret generic kafka-ui-oauth2-secret \
  --from-literal=OAUTH2_CLIENT_ID="867203584040-j7pfakbprggkdn8b8iseglm7ivpbqcuo.apps.googleusercontent.com" \
  --from-literal=OAUTH2_CLIENT_SECRET="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" \
  -n testns

# Verify secret creation
kubectl get secret kafka-ui-oauth2-secret -n testns
```

### Step 2: Deploy Kafka UI
```bash
# Deploy/upgrade with custom configuration
helm upgrade --install kafka-ui kafka-ui -n testns --values kafka-ui/custom-values.yaml
```

### Step 3: Verify Deployment
```bash
# Check pods are running
kubectl get pods -l app.kubernetes.io/name=kafka-ui -n testns

# Check ingress is created
kubectl get ingress -n testns

# Check SSL certificate
kubectl get secrets kafka-ui.strawmine.com-tls -n testns

# Verify OAuth2 environment variables are loaded
kubectl exec -it <kafka-ui-pod> -n testns -- env | grep OAUTH2

# Check Helm release status
helm status kafka-ui -n testns
```

## Security Features

### ✅ Current Security Implementation

1. **Secure OAuth2 Credentials**
   - OAuth2 credentials are stored in Kubernetes secrets
   - No plain text secrets in configuration files
   - Variable substitution used for credential injection

2. **Secret Management**
   ```yaml
   # Current secure implementation
   existingSecret: "kafka-ui-oauth2-secret"

   # Variables used in configuration
   clientId: ${OAUTH2_CLIENT_ID}
   clientSecret: ${OAUTH2_CLIENT_SECRET}
   ```

### Best Practices

1. **Credential Rotation**
   - Rotate OAuth2 credentials regularly
   - Update Google OAuth2 application settings

2. **Access Review**
   - Periodically review user access lists
   - Remove inactive users from admin role

3. **Monitoring**
   - Monitor resource usage
   - Set up alerts for authentication failures
   - Track user access patterns

## Troubleshooting

### Common Issues

1. **OAuth2 Authentication Failures**
   - Verify Google OAuth2 application configuration
   - Check domain restrictions
   - Validate client ID and secret

2. **SSL Certificate Issues**
   - Ensure cert-manager is installed and configured
   - Check Let's Encrypt rate limits
   - Verify DNS resolution for the domain

3. **Resource Constraints**
   - Monitor pod resource usage
   - Adjust limits if needed
   - Check for memory leaks

### Useful Commands

```bash
# Check pod logs
kubectl logs -l app.kubernetes.io/name=kafka-ui -n testns

# Check ingress status
kubectl describe ingress kafka-ui -n testns

# Check certificate status
kubectl describe certificate kafka-ui.strawmine.com-tls -n testns

# Check resource usage
kubectl top pods -l app.kubernetes.io/name=kafka-ui -n testns

# Check Helm deployment
helm list -n testns
helm get values kafka-ui -n testns
```

## Maintenance Tasks

### Regular Maintenance
- [ ] Review and update user access lists monthly
- [ ] Monitor resource usage and adjust limits quarterly
- [ ] Rotate OAuth2 credentials every 6 months
- [ ] Review and update RBAC permissions annually

### Security Audits
- [ ] Audit user access logs
- [ ] Review authentication patterns
- [ ] Validate SSL certificate expiration
- [ ] Check for security updates

## Final Configuration Summary

Your `custom-values.yaml` implements a secure, production-ready Kafka UI deployment with:

### Security
- ✅ **OAuth2 credentials in Kubernetes secrets** (not in config files)
- ✅ **Variable substitution** using `${OAUTH2_CLIENT_ID}` and `${OAUTH2_CLIENT_SECRET}`
- ✅ **Domain-restricted authentication** (solvei8.com only)
- ✅ **SSL/TLS encryption** with automatic certificate management

### Access Control
- ✅ **7 named admin users** with full Kafka management permissions
- ✅ **Domain-wide read-only access** for all solvei8.com users
- ✅ **Granular RBAC permissions** for topics, consumers, schemas, connectors

### Infrastructure
- ✅ **Production resource limits** (300m CPU, 712Mi memory)
- ✅ **External ingress** with NGINX controller
- ✅ **Custom domain** (kafka-ui.strawmine.com)
- ✅ **Let's Encrypt SSL certificates**

### Deployment
- ✅ **Simple secret creation** with kubectl command
- ✅ **Standard Helm deployment** process
- ✅ **Easy credential rotation** without config changes

This configuration provides enterprise-grade security and functionality for your Kafka UI deployment.
