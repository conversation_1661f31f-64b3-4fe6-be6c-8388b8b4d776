# Kafka UI Custom Values Documentation

This document provides comprehensive documentation for the `custom-values.yaml` file, explaining every configuration parameter and its purpose.

## File Overview

**File**: `kafka-ui/custom-values.yaml`  
**Purpose**: Production-ready Kafka UI configuration with secure OAuth2 authentication, RBAC, and SSL ingress  
**Dependencies**: Requires `kafka-ui-oauth2-secret` Kubernetes secret

## Complete Configuration Breakdown

### 1. Secret Reference (Line 1)

```yaml
existingSecret: "kafka-ui-oauth2-secret"
```

**Purpose**: References a Kubernetes secret containing OAuth2 credentials  
**Behavior**: Loads all environment variables from the specified secret into the Kafka UI pod  
**Required Secret Keys**:
- `OAUTH2_CLIENT_ID`: Google OAuth2 client ID
- `OAUTH2_CLIENT_SECRET`: Google OAuth2 client secret

**Create Secret Command**:
```bash
kubectl create secret generic kafka-ui-oauth2-secret \
  --from-literal=OAUTH2_CLIENT_ID="************-j7pfakbprggkdn8b8iseglm7ivpbqcuo.apps.googleusercontent.com" \
  --from-literal=OAUTH2_CLIENT_SECRET="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" \
  -n testns
```

### 2. Application Configuration (Lines 2-97)

#### 2.1 Kafka Cluster Configuration (Lines 3-6)

```yaml
yamlApplicationConfig:
  kafka:
    clusters:
      - name: confluent-4.1.1
        bootstrapServers: ***********:9093,***********:9093,************:9093
```

**Configuration Details**:
- **Cluster Name**: `confluent-4.1.1` - Identifier for the Kafka cluster
- **Bootstrap Servers**: Three Kafka broker endpoints
  - `***********:9093` - Primary broker
  - `***********:9093` - Secondary broker  
  - `************:9093` - Tertiary broker
- **Port**: `9093` - Typically used for SSL/SASL secured connections
- **Connection Type**: Direct IP connection (no service discovery)

#### 2.2 Authentication Configuration (Lines 8-19)

```yaml
auth:
  type: OAUTH2
  oauth2:
    client:
      google:
        provider: google
        clientId: ${OAUTH2_CLIENT_ID}
        clientSecret: ${OAUTH2_CLIENT_SECRET}
        user-name-attribute: email
        custom-params:
          type: google
          allowedDomain: solvei8.com
```

**Authentication Details**:
- **Type**: `OAUTH2` - Uses OAuth2 authentication protocol
- **Provider**: `google` - Google OAuth2 service
- **Client ID**: `${OAUTH2_CLIENT_ID}` - Variable substitution from secret
- **Client Secret**: `${OAUTH2_CLIENT_SECRET}` - Variable substitution from secret
- **User Attribute**: `email` - Uses email as the primary user identifier
- **Domain Restriction**: `solvei8.com` - Only users from this domain can authenticate
- **Custom Parameters**: Additional Google-specific OAuth2 settings

#### 2.3 Role-Based Access Control (Lines 21-96)

##### Admin Role (Lines 23-68)

```yaml
rbac:
  roles:
    - name: "admin"
      clusters:
        - confluent-4.1.1
      subjects:
        - provider: oauth_google
          type: user
          value: "<EMAIL>"
        # ... (7 total admin users)
      permissions:
        - resource: topic
          value: ".*"
          actions: [ "all" ]
        # ... (additional permissions)
```

**Admin Role Configuration**:
- **Role Name**: `admin`
- **Target Cluster**: `confluent-4.1.1`
- **Authorized Users** (7 total):
  1. `<EMAIL>`
  2. `<EMAIL>`
  3. `<EMAIL>`
  4. `<EMAIL>`
  5. `<EMAIL>`
  6. `<EMAIL>`
  7. `<EMAIL>`

**Admin Permissions**:
- **Topics**: Full access (`all` actions on `.*` pattern)
- **Consumers**: Full access (`all` actions on `.*` pattern)
- **Schemas**: Full access (`all` actions on `.*` pattern)
- **Connectors**: Full access (`all` actions on `.*` pattern)
- **KSQL**: Execute queries
- **ACLs**: View and edit access control lists
- **Application Config**: View and edit application configuration
- **Cluster Config**: View and edit cluster configuration

##### Read-Only Role (Lines 70-96)

```yaml
- name: "read-only"
  clusters:
    - confluent-4.1.1
  subjects:
    - provider: oauth_google
      type: domain
      value: "solvei8.com"
  permissions:
    - resource: topic
      value: ".*"
      actions:
        - VIEW
        - MESSAGES_READ
        - ANALYSIS_VIEW
    # ... (additional read-only permissions)
```

**Read-Only Role Configuration**:
- **Role Name**: `read-only`
- **Target Cluster**: `confluent-4.1.1`
- **Authorized Users**: All users from `solvei8.com` domain (domain-wide access)

**Read-Only Permissions**:
- **Topics**: View, read messages, view analysis
- **Consumers**: View only
- **Schemas**: View only
- **Connectors**: View only
- **ACLs**: View only
- **Cluster Config**: View only

### 3. Resource Management (Lines 99-105)

```yaml
resources:
  limits:
    cpu: 300m
    memory: 712Mi
  requests:
    cpu: 200m
    memory: 512Mi
```

**Resource Allocation**:
- **CPU Request**: `200m` (0.2 CPU cores) - Guaranteed CPU allocation
- **CPU Limit**: `300m` (0.3 CPU cores) - Maximum CPU usage
- **Memory Request**: `512Mi` (512 MiB) - Guaranteed memory allocation
- **Memory Limit**: `712Mi` (712 MiB) - Maximum memory usage

**Sizing Rationale**:
- Suitable for medium-scale Kafka clusters
- Provides 50% CPU headroom for peak usage
- 39% memory headroom for growth
- Prevents resource starvation of other pods

### 4. Network Exposure Configuration (Lines 108-117)

```yaml
ingress:
  enabled: true
  annotations: 
    cert-manager.io/cluster-issuer: letsencrypt-issuer
  ingressClassName: "external-nginx"
  path: "/"
  host: "kafka-ui.strawmine.com"
  tls:
    enabled: true
    secretName: "kafka-ui.strawmine.com-tls"
```

**Ingress Configuration**:
- **Enabled**: `true` - Ingress is active
- **Annotations**: `cert-manager.io/cluster-issuer: letsencrypt-issuer` - Automatic SSL certificate management
- **Ingress Class**: `external-nginx` - Uses external NGINX ingress controller
- **Path**: `/` - Root path routing
- **Host**: `kafka-ui.strawmine.com` - Custom domain
- **TLS Enabled**: `true` - SSL/TLS encryption enabled
- **Certificate Secret**: `kafka-ui.strawmine.com-tls` - Secret storing SSL certificate

**SSL Certificate Management**:
- **Provider**: Let's Encrypt via cert-manager
- **Automatic Renewal**: Yes (handled by cert-manager)
- **Certificate Type**: Domain Validated (DV)

## Deployment Instructions

### Prerequisites

1. **Kubernetes Cluster**: Running cluster with RBAC enabled
2. **Helm**: Version 3.x installed
3. **Cert-Manager**: Installed for SSL certificate management
4. **NGINX Ingress Controller**: External ingress controller configured
5. **DNS**: `kafka-ui.strawmine.com` pointing to ingress controller

### Step-by-Step Deployment

#### Step 1: Create OAuth2 Secret
```bash
kubectl create secret generic kafka-ui-oauth2-secret \
  --from-literal=OAUTH2_CLIENT_ID="************-j7pfakbprggkdn8b8iseglm7ivpbqcuo.apps.googleusercontent.com" \
  --from-literal=OAUTH2_CLIENT_SECRET="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" \
  -n testns
```

#### Step 2: Deploy Kafka UI
```bash
helm upgrade --install kafka-ui kafka-ui -n testns --values kafka-ui/custom-values.yaml
```

#### Step 3: Verify Deployment
```bash
# Check pod status
kubectl get pods -l app.kubernetes.io/name=kafka-ui -n testns

# Check ingress
kubectl get ingress -n testns

# Check SSL certificate
kubectl get certificate -n testns

# Verify OAuth2 variables
kubectl exec -it <kafka-ui-pod> -n testns -- env | grep OAUTH2
```

## Security Features

### Authentication Security
- ✅ **OAuth2 Integration**: Google OAuth2 provider
- ✅ **Domain Restriction**: Only `solvei8.com` users
- ✅ **Secure Credential Storage**: Kubernetes secrets
- ✅ **Variable Substitution**: No plain text secrets in config

### Authorization Security  
- ✅ **Role-Based Access Control**: Granular permissions
- ✅ **Principle of Least Privilege**: Read-only default access
- ✅ **Named Admin Users**: Specific admin permissions
- ✅ **Resource-Level Permissions**: Fine-grained access control

### Network Security
- ✅ **SSL/TLS Encryption**: HTTPS-only access
- ✅ **Automatic Certificate Management**: Let's Encrypt integration
- ✅ **External Ingress**: Isolated network access
- ✅ **Custom Domain**: Professional domain setup

## Operational Considerations

### Monitoring
- Monitor resource usage against defined limits
- Track authentication success/failure rates
- Monitor SSL certificate expiration
- Watch for RBAC permission violations

### Maintenance
- **Monthly**: Review user access lists
- **Quarterly**: Adjust resource limits based on usage
- **Semi-annually**: Rotate OAuth2 credentials
- **Annually**: Review and update RBAC permissions

### Troubleshooting
- Check pod logs for authentication issues
- Verify secret exists and contains correct values
- Ensure DNS resolution for custom domain
- Validate cert-manager certificate issuance

## Configuration Examples

### Example 1: Adding a New Admin User
To add a new admin user, update the RBAC section:

```yaml
rbac:
  roles:
    - name: "admin"
      subjects:
        # Existing users...
        - provider: oauth_google
          type: user
          value: "<EMAIL>"  # Add new user here
```

### Example 2: Creating a Custom Role
Add a new role with specific permissions:

```yaml
rbac:
  roles:
    # Existing roles...
    - name: "topic-manager"
      clusters:
        - confluent-4.1.1
      subjects:
        - provider: oauth_google
          type: user
          value: "<EMAIL>"
      permissions:
        - resource: topic
          value: ".*"
          actions: [ "VIEW", "CREATE", "EDIT", "DELETE" ]
        - resource: consumer
          value: ".*"
          actions: [ "view" ]
```

### Example 3: Environment-Specific Configuration
For different environments, modify key parameters:

```yaml
# Development Environment
yamlApplicationConfig:
  kafka:
    clusters:
      - name: dev-kafka
        bootstrapServers: dev-kafka:9092  # Non-SSL for dev

# Production Environment (current config)
yamlApplicationConfig:
  kafka:
    clusters:
      - name: confluent-4.1.1
        bootstrapServers: ***********:9093,***********:9093,************:9093
```

## Troubleshooting Guide

### Common Issues and Solutions

#### 1. OAuth2 Authentication Failures

**Symptoms**:
- Users cannot log in
- "Authentication failed" errors
- Redirect loops

**Diagnosis**:
```bash
# Check OAuth2 environment variables
kubectl exec -it <kafka-ui-pod> -n testns -- env | grep OAUTH2

# Check pod logs for auth errors
kubectl logs -l app.kubernetes.io/name=kafka-ui -n testns | grep -i oauth
```

**Solutions**:
- Verify Google OAuth2 application configuration
- Check domain restrictions in Google Console
- Ensure redirect URIs are correctly configured
- Validate client ID and secret in the secret

#### 2. SSL Certificate Issues

**Symptoms**:
- Browser SSL warnings
- Certificate not found errors
- HTTPS not working

**Diagnosis**:
```bash
# Check certificate status
kubectl get certificate -n testns
kubectl describe certificate kafka-ui.strawmine.com-tls -n testns

# Check cert-manager logs
kubectl logs -n cert-manager deployment/cert-manager
```

**Solutions**:
- Verify cert-manager is installed and running
- Check Let's Encrypt rate limits
- Ensure DNS resolution for the domain
- Validate cluster issuer configuration

#### 3. RBAC Permission Issues

**Symptoms**:
- Users see "Access Denied" messages
- Missing UI elements for authorized users
- Incorrect permission levels

**Diagnosis**:
```bash
# Check current user's permissions in UI
# Look for RBAC-related logs
kubectl logs -l app.kubernetes.io/name=kafka-ui -n testns | grep -i rbac
```

**Solutions**:
- Verify user email matches RBAC configuration exactly
- Check provider type (oauth_google) is correct
- Ensure cluster name matches in RBAC rules
- Validate permission syntax and resource names

#### 4. Resource Constraints

**Symptoms**:
- Pod restarts frequently
- Slow UI performance
- Out of memory errors

**Diagnosis**:
```bash
# Check resource usage
kubectl top pods -l app.kubernetes.io/name=kafka-ui -n testns

# Check pod events
kubectl describe pod -l app.kubernetes.io/name=kafka-ui -n testns
```

**Solutions**:
- Increase memory limits if needed
- Adjust CPU requests based on usage patterns
- Monitor long-term resource trends
- Consider horizontal scaling if supported

### Useful Diagnostic Commands

```bash
# Complete deployment status
helm status kafka-ui -n testns

# Get all Kafka UI related resources
kubectl get all -l app.kubernetes.io/name=kafka-ui -n testns

# Check ingress configuration
kubectl describe ingress kafka-ui -n testns

# View complete pod configuration
kubectl get pod <kafka-ui-pod> -n testns -o yaml

# Check secret contents (base64 encoded)
kubectl get secret kafka-ui-oauth2-secret -n testns -o yaml

# Test connectivity to Kafka brokers
kubectl exec -it <kafka-ui-pod> -n testns -- nc -zv *********** 9093
```

## Advanced Configuration

### Custom Environment Variables
Add additional environment variables if needed:

```yaml
# Add to custom-values.yaml if you need additional env vars
env:
  CUSTOM_SETTING: "value"
  ANOTHER_SETTING: "another-value"
```

### Multiple Kafka Clusters
Configure multiple Kafka clusters:

```yaml
yamlApplicationConfig:
  kafka:
    clusters:
      - name: confluent-4.1.1
        bootstrapServers: ***********:9093,***********:9093,************:9093
      - name: dev-cluster
        bootstrapServers: dev-kafka:9092
      - name: staging-cluster
        bootstrapServers: staging-kafka:9092
```

### Custom Annotations and Labels
Add custom metadata:

```yaml
# Add to custom-values.yaml
annotations:
  monitoring.coreos.com/scrape: "true"
  prometheus.io/port: "8080"

labels:
  environment: "production"
  team: "data-platform"
```

## Security Best Practices

### 1. Secret Management
- ✅ Use Kubernetes secrets for all sensitive data
- ✅ Implement secret rotation procedures
- ✅ Use namespace isolation
- ✅ Add appropriate RBAC for secret access

### 2. Network Security
- ✅ Use TLS for all communications
- ✅ Implement network policies if needed
- ✅ Restrict ingress to necessary sources
- ✅ Use private container registries

### 3. Access Control
- ✅ Follow principle of least privilege
- ✅ Regular access reviews
- ✅ Audit user activities
- ✅ Use domain restrictions

### 4. Monitoring and Alerting
- ✅ Monitor authentication failures
- ✅ Track resource usage
- ✅ Alert on certificate expiration
- ✅ Log security events

## Maintenance Procedures

### Regular Tasks

#### Weekly
- [ ] Check pod health and resource usage
- [ ] Review authentication logs for anomalies
- [ ] Verify SSL certificate status

#### Monthly
- [ ] Review user access lists
- [ ] Update RBAC permissions as needed
- [ ] Check for Kafka UI updates
- [ ] Validate backup procedures

#### Quarterly
- [ ] Analyze resource usage trends
- [ ] Adjust resource limits if needed
- [ ] Review security configurations
- [ ] Update documentation

#### Semi-Annually
- [ ] Rotate OAuth2 credentials
- [ ] Security audit and penetration testing
- [ ] Review and update disaster recovery procedures
- [ ] Validate compliance requirements

#### Annually
- [ ] Complete RBAC permission review
- [ ] Update security policies
- [ ] Review and update monitoring strategies
- [ ] Plan capacity for next year

This comprehensive configuration provides enterprise-grade security, scalability, and maintainability for your Kafka UI deployment.
