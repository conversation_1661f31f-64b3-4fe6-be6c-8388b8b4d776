# Kafka UI Custom Values Configuration Documentation

This document explains the various parameters configured in your `custom-values.yaml` file for the Kafka UI Helm chart. This file overrides the default settings provided by the chart's `values.yaml` and directly influences the Kafka UI application's behavior and deployment.

## `yamlApplicationConfig`

This is a critical section that directly maps to the Kafka UI application's internal `config.yml`. Any configuration defined here will be used by the Kafka UI application itself.

### `kafka`

This section configures the Kafka clusters that Kafka UI will connect to.

*   **`clusters`**: A list of Kafka cluster configurations.
    *   **`- name`**: (String) A unique, human-readable name for the Kafka cluster within Kafka UI.
        *   *Example:* `confluent-4.1.1`
    *   **`bootstrapServers`**: (String) A comma-separated list of Kafka broker addresses (host:port) for the cluster.
        *   *Example:* `***********:9093,***********:9093,************:9093`

### `auth`

This section configures the authentication method for Kafka UI.

*   **`type`**: (String) Specifies the authentication mechanism.
    *   *Example:* `OAUTH2` (indicating OAuth 2.0 authentication)
*   **`oauth2`**: Configuration specific to OAuth 2.0.
    *   **`client`**: Client-specific OAuth 2.0 settings.
        *   **`google`**: Configuration for Google as an OAuth 2.0 provider.
            *   **`provider`**: (String) The name of the OAuth provider.
                *   *Example:* `google`
            *   **`clientId`**: (String) The client ID obtained from your Google OAuth 2.0 application. This identifies your application to Google.
                *   *Example:* `867203584040-j7pfakbprggkdn8b8iseglm7ivpbqcuo.apps.googleusercontent.com`
            *   **`clientSecret`**: (String) The client secret obtained from your Google OAuth 2.0 application. This is a sensitive value used to authenticate your application with Google.
                *   *Example:* `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`
                *   **Security Note:** Storing `clientSecret` directly in `custom-values.yaml` is not recommended for production environments. Consider using Kubernetes Secrets to manage this sensitive information.
            *   **`user-name-attribute`**: (String) The attribute from the OAuth 2.0 user profile that will be used as the username in Kafka UI.
                *   *Example:* `email`
            *   **`custom-params`**: Additional custom parameters for the OAuth provider.
                *   **`type`**: (String) A custom type identifier for the OAuth provider.
                    *   *Example:* `google`
                *   **`allowedDomain`**: (String) Restricts access to users from a specific domain. Only users with email addresses from this domain will be allowed to log in.
                    *   *Example:* `solvei8.com`

### `rbac` (Role-Based Access Control)

This section defines roles and permissions within Kafka UI, allowing granular control over user actions.

*   **`roles`**: A list of role definitions. Each role specifies a set of permissions and the subjects (users/groups) that belong to it.
    *   **`- name`**: (String) A unique name for the role.
        *   *Example:* `"admin"`, `"read-only"`
    *   **`clusters`**: (List of Strings) Specifies which Kafka clusters this role's permissions apply to.
        *   *Example:* `- confluent-4.1.1` (meaning this role applies to the cluster named "confluent-4.1.1")
    *   **`subjects`**: A list of entities (users or groups) that are assigned to this role. The `provider` and `type` determine how the `value` is interpreted.
        *   **`- provider`**: (String) The authentication provider from which the subject originates.
            *   *Example:* `oauth_google` (for subjects coming from Google OAuth)
        *   **`type`**: (String) The type of subject.
            *   *Example:* `user` (for individual users), `domain` (for all users within a specific domain)
        *   **`value`**: (String) The identifier for the subject.
            *   *Example (for `type: user`):* `"<EMAIL>"` (an individual user's email)
            *   *Example (for `type: domain`):* `"solvei8.com"` (all users in this domain)
    *   **`permissions`**: A list of permissions granted to this role. Each permission defines access to a specific Kafka resource and allowed actions.
        *   **`- resource`**: (String) The type of Kafka resource the permission applies to.
            *   *Examples:* `topic`, `consumer`, `schema`, `connect`, `ksql`, `acl`, `applicationconfig`, `clusterconfig`
        *   **`value`**: (String) A regular expression or fixed string identifying the specific resource instance. For some resources (like `applicationconfig`, `clusterconfig`, `ksql`, `acl`), this field is not applicable and should be omitted or set to `".*"` for all instances.
            *   *Example:* `".*"` (applies to all topics)
        *   **`actions`**: (List of Strings) A list of operations allowed on the specified resource.
            *   *Example (for `topic` resource):*
                *   `VIEW`: View topic details.
                *   `CREATE`: Create new topics.
                *   `EDIT`: Modify topic configurations.
                *   `DELETE`: Delete topics.
                *   `MESSAGES_READ`: Read messages from topics.
                *   `MESSAGES_PRODUCE`: Produce messages to topics.
                *   `MESSAGES_DELETE`: Delete messages from topics.
                *   `ANALYSIS_RUN`, `ANALYSIS_VIEW`: Actions related to topic analysis.
                *   `all`: A special action that grants all possible actions for the given resource.
            *   *Example (for `consumer` resource):* `VIEW`, `DELETE`, `RESET_OFFSETS`
            *   *Example (for `schema` resource):* `VIEW`, `CREATE`, `DELETE`, `EDIT`, `MODIFY_GLOBAL_COMPATIBILITY`
            *   *Example (for `connect` resource):* `VIEW`, `EDIT`, `CREATE`, `DELETE`, `OPERATE`, `RESET_OFFSETS`
            *   *Example (for `ksql` resource):* `EXECUTE`
            *   *Example (for `acl` resource):* `VIEW`, `EDIT`
            *   *Example (for `applicationconfig`, `clusterconfig` resources):* `VIEW`, `EDIT`

## `resources`

This section defines the CPU and memory resources allocated to the Kafka UI application's Kubernetes pod.

*   **`limits`**: The maximum amount of CPU and memory the container is allowed to use. If the container tries to use more than these limits, it might be throttled or terminated.
    *   **`cpu`**: (String) CPU limit (e.g., `300m` for 300 millicores).
    *   **`memory`**: (String) Memory limit (e.g., `712Mi` for 712 mebibytes).
*   **`requests`**: The minimum amount of CPU and memory guaranteed to the container. The Kubernetes scheduler uses these values to decide which node to place the pod on.
    *   **`cpu`**: (String) CPU request (e.g., `200m`).
    *   **`memory`**: (String) Memory request (e.g., `512Mi`).

## `ingress`

This section configures the Kubernetes Ingress resource, which exposes the Kafka UI application to external traffic.

*   **`enabled`**: (Boolean) If `true`, an Ingress resource will be created.
*   **`annotations`**: (Object) A map of annotations to add to the Ingress resource. These are often used by Ingress controllers (like `cert-manager` for TLS certificates).
    *   *Example:* `cert-manager.io/cluster-issuer: letsencrypt-issuer` (tells cert-manager to use a specific issuer for TLS certificates)
*   **`ingressClassName`**: (String) Specifies the IngressClass to be used by this Ingress. This is important if you have multiple Ingress controllers in your cluster.
    *   *Example:* `"external-nginx"`
*   **`path`**: (String) The URL path where the application will be accessible.
    *   *Example:* `"/"` (root path)
*   **`host`**: (String) The hostname (domain name) at which the application will be accessible.
    *   *Example:* `"kafka-ui.strawmine.com"`
*   **`tls`**: Configuration for TLS (HTTPS) encryption.
    *   **`enabled`**: (Boolean) If `true`, TLS will be enabled for the Ingress.
    *   **`secretName`**: (String) The name of the Kubernetes Secret that will store the TLS certificate and key.
        *   *Example:* `"kafka-ui.strawmine.com-tls"` (This secret is typically managed by `cert-manager` if annotations are present).
