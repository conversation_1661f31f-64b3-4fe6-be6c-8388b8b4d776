# Kafka UI Helm Chart

## Overview

This Helm chart deploys Kafka UI with OAuth2 authentication, role-based access control (RBAC), and SSL-enabled ingress on a Kubernetes cluster.

## Prerequisites

- Kubernetes 1.19+
- Helm 3.2.0+
- cert-manager (for SSL certificates)
- NGINX Ingress Controller

## Quick Start

```bash
# Create OAuth2 secret
kubectl create secret generic kafka-ui-oauth2-secret \
  --from-literal=OAUTH2_CLIENT_ID="your-client-id" \
  --from-literal=OAUTH2_CLIENT_SECRET="your-client-secret" \
  --namespace=testns

# Install the chart
helm upgrade --install kafka-ui kafka-ui \
  --namespace=testns \
  --values kafka-ui/custom-values.yaml
```

## Installing the Chart

To install the chart with the release name `kafka-ui`:

```bash
helm upgrade --install kafka-ui kafka-ui \
  --namespace=testns \
  --values kafka-ui/custom-values.yaml
```

The command deploys Kafka UI on the Kubernetes cluster with the specified configuration.

## Uninstalling the Chart

To uninstall/delete the `kafka-ui` deployment:

```bash
helm uninstall kafka-ui --namespace=testns
```

## Configuration

### Authentication

This chart uses OAuth2 authentication with Google as the identity provider.

#### Creating OAuth2 Secret

Create a Kubernetes secret containing OAuth2 credentials:

```bash
kubectl create secret generic kafka-ui-oauth2-secret \
  --from-literal=OAUTH2_CLIENT_ID="867203584040-j7pfakbprggkdn8b8iseglm7ivpbqcuo.apps.googleusercontent.com" \
  --from-literal=OAUTH2_CLIENT_SECRET="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" \
  --namespace=testns
```

#### OAuth2 Configuration

```yaml
existingSecret: "kafka-ui-oauth2-secret"

yamlApplicationConfig:
  auth:
    type: OAUTH2
    oauth2:
      client:
        google:
          provider: google
          clientId: ${OAUTH2_CLIENT_ID}
          clientSecret: ${OAUTH2_CLIENT_SECRET}
          user-name-attribute: email
          custom-params:
            type: google
            allowedDomain: solvei8.com
```

### Kafka Cluster Configuration

```yaml
yamlApplicationConfig:
  kafka:
    clusters:
      - name: confluent-4.1.1
        bootstrapServers: ***********:9093,***********:9093,************:9093
```

### Role-Based Access Control

The chart includes two predefined roles:

#### Admin Role
- **Users**: 7 specific admin users
- **Permissions**: Full access to all Kafka UI features

#### Read-Only Role
- **Users**: All users from the allowed domain
- **Permissions**: View-only access

### Resource Configuration

```yaml
resources:
  limits:
    cpu: 300m
    memory: 712Mi
  requests:
    cpu: 200m
    memory: 512Mi
```

### Ingress Configuration

```yaml
ingress:
  enabled: true
  annotations: 
    cert-manager.io/cluster-issuer: letsencrypt-issuer
  ingressClassName: "external-nginx"
  path: "/"
  host: "kafka-ui.strawmine.com"
  tls:
    enabled: true
    secretName: "kafka-ui.strawmine.com-tls"
```

## Parameters

### Global Parameters

| Name | Description | Value |
|------|-------------|-------|
| `existingSecret` | Name of existing secret containing environment variables | `kafka-ui-oauth2-secret` |

### Application Parameters

| Name | Description | Value |
|------|-------------|-------|
| `yamlApplicationConfig.kafka.clusters[0].name` | Kafka cluster name | `confluent-4.1.1` |
| `yamlApplicationConfig.kafka.clusters[0].bootstrapServers` | Kafka bootstrap servers | `***********:9093,***********:9093,************:9093` |

### Authentication Parameters

| Name | Description | Value |
|------|-------------|-------|
| `yamlApplicationConfig.auth.type` | Authentication type | `OAUTH2` |
| `yamlApplicationConfig.auth.oauth2.client.google.provider` | OAuth2 provider | `google` |
| `yamlApplicationConfig.auth.oauth2.client.google.allowedDomain` | Allowed domain | `solvei8.com` |

### Resource Parameters

| Name | Description | Value |
|------|-------------|-------|
| `resources.limits.cpu` | CPU resource limits | `300m` |
| `resources.limits.memory` | Memory resource limits | `712Mi` |
| `resources.requests.cpu` | CPU resource requests | `200m` |
| `resources.requests.memory` | Memory resource requests | `512Mi` |

### Ingress Parameters

| Name | Description | Value |
|------|-------------|-------|
| `ingress.enabled` | Enable ingress | `true` |
| `ingress.ingressClassName` | Ingress class name | `external-nginx` |
| `ingress.host` | Hostname | `kafka-ui.strawmine.com` |
| `ingress.tls.enabled` | Enable TLS | `true` |
| `ingress.tls.secretName` | TLS secret name | `kafka-ui.strawmine.com-tls` |

## Troubleshooting

### OAuth2 Authentication Issues

Check OAuth2 environment variables:

```bash
kubectl exec -it <kafka-ui-pod> -n testns -- env | grep OAUTH2
```

### SSL Certificate Issues

Check certificate status:

```bash
kubectl get certificate -n testns
kubectl describe certificate kafka-ui.strawmine.com-tls -n testns
```

### General Troubleshooting

```bash
# Check deployment status
helm status kafka-ui -n testns

# View pod logs
kubectl logs -l app.kubernetes.io/name=kafka-ui -n testns

# Check ingress status
kubectl describe ingress kafka-ui -n testns

# Check resource usage
kubectl top pods -l app.kubernetes.io/name=kafka-ui -n testns
```

## Upgrading

To upgrade the deployment:

```bash
helm upgrade kafka-ui kafka-ui \
  --namespace=testns \
  --values kafka-ui/custom-values.yaml
```

## Security Considerations

- OAuth2 credentials are stored in Kubernetes secrets
- Domain-based access restriction is enforced
- SSL/TLS encryption is enabled for all traffic
- RBAC provides fine-grained access control

## Support

For issues and questions, please refer to the [Kafka UI documentation](https://ui.docs.kafbat.io/) or create an issue in the project repository.
