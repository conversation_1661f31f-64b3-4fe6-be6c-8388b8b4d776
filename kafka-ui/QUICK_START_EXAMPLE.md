# Quick Start: Secure OAuth2 Configuration

This is a quick example showing exactly how to use `existingSecret` with variable substitution in your Kafka UI configuration.

## 1. Create the Secret

```bash
kubectl create secret generic kafka-ui-oauth2-secret \
  --from-literal=OAUTH2_CLIENT_ID="867203584040-j7pfakbprggkdn8b8iseglm7ivpbqcuo.apps.googleusercontent.com" \
  --from-literal=OAUTH2_CLIENT_SECRET="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"
```

## 2. Use in Helm Values

```yaml
# Reference the secret
existingSecret: "kafka-ui-oauth2-secret"

# Use variables in OAuth2 configuration
yamlApplicationConfig:
  auth:
    type: OAUTH2
    oauth2:
      client:
        google:
          provider: google
          clientId: ${OAUTH2_CLIENT_ID}        # ← Variable from secret
          clientSecret: ${OAUTH2_CLIENT_SECRET} # ← Variable from secret
          user-name-attribute: email
          custom-params:
            type: google
            allowedDomain: solvei8.com
```

## 3. Deploy

```bash
helm install kafka-ui . -f custom-values-with-secret.yaml
```

## How It Works

1. **existingSecret**: Tells Helm to load ALL environment variables from the secret
2. **${VARIABLE_NAME}**: Kafka UI replaces these with actual environment variable values
3. **Runtime**: OAuth2 works with the injected credentials

## Verify It's Working

```bash
# Check environment variables are loaded
kubectl exec -it <kafka-ui-pod> -- env | grep OAUTH2

# Should show:
# OAUTH2_CLIENT_ID=867203584040-j7pfakbprggkdn8b8iseglm7ivpbqcuo.apps.googleusercontent.com
# OAUTH2_CLIENT_SECRET=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
```

That's it! Your OAuth2 credentials are now secure and not stored in your values files.
