yamlApplicationConfig:
  kafka:
    clusters:
      - name: confluent-4.1.1
        bootstrapServers: 10.0.136.28:9093,10.0.149.84:9093,10.0.152.181:9093   
  # auth:
  #   type: LOGIN_FORM

  # spring:
  #   security:
  #     user:
  #       name: admin
  #       password: password
  auth:
    type: OAUTH2
    oauth2:
      client:
        google:
          provider: google
          # OAuth2 credentials are now loaded from Kubernetes secret
          # clientId and clientSecret will be injected via environment variables
          user-name-attribute: email
          custom-params:
            type: google
            allowedDomain: solvei8.com # for RBAC

  rbac:
    roles:
      - name: "admin"
        clusters:
          - confluent-4.1.1
        subjects:
          - provider: oauth_google
            type: user
            value: "<EMAIL>"
          - provider: oauth_google
            type: user
            value: "<EMAIL>"
          - provider: oauth_google
            type: user
            value: "<EMAIL>"
          - provider: oauth_google
            type: user
            value: "<EMAIL>"
          - provider: oauth_google
            type: user
            value: "<EMAIL>"
          - provider: oauth_google
            type: user
            value: "<EMAIL>"
          - provider: oauth_google
            type: user
            value: "<EMAIL>"
        permissions:
          - resource: topic
            value: ".*"
            actions: [ "all" ]
          - resource: consumer
            value: ".*"
            actions: [ "all" ]
          - resource: schema
            value: ".*"
            actions: [ "all" ]
          - resource: connect
            value: ".*"
            actions: [ "all" ]
          - resource: ksql
            actions: [ "execute" ]
          - resource: acl
            actions: [ "view", "edit" ]
          - resource: applicationconfig
            actions: [ "view", "edit" ]
          - resource: clusterconfig
            actions: [ "view", "edit" ]

      - name: "read-only"
        clusters:
          - confluent-4.1.1
        subjects:
          - provider: oauth_google
            type: user
            value: "<EMAIL>"
          - provider: oauth_google
            type: domain
            value: "solvei8.com"
        permissions:
          - resource: topic
            value: ".*"
            actions:
              - VIEW
              - MESSAGES_READ
              - ANALYSIS_VIEW
          - resource: consumer
            value: ".*"
            actions: [ "view" ]
          - resource: schema
            value: ".*"
            actions: [ "view" ]
          - resource: connect
            value: ".*"
            actions: [ "view" ]
          - resource: acl
            actions: [ "view" ]
          - resource: clusterconfig
            actions: [ "view" ]

# Environment variables from Kubernetes secret
envs:
  secretMappings:
    # Map OAuth2 client ID from secret
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_GOOGLE_CLIENT_ID:
      name: kafka-ui-oauth2-secret
      keyName: client-id
    # Map OAuth2 client secret from secret
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_GOOGLE_CLIENT_SECRET:
      name: kafka-ui-oauth2-secret
      keyName: client-secret

resources:
  limits:
    cpu: 300m
    memory: 712Mi
  requests:
    cpu: 200m
    memory: 512Mi

ingress:
  enabled: true
  annotations: 
    cert-manager.io/cluster-issuer: letsencrypt-issuer
  ingressClassName: "external-nginx"
  path: "/"
  host: "kafka-ui.strawmine.com"
  tls:
    enabled: true
    secretName: "kafka-ui.strawmine.com-tls"
