apiVersion: v1
kind: Secret
metadata:
  name: kafka-ui-oauth2-secret
  namespace: default  # Change to your target namespace
  labels:
    app.kubernetes.io/name: kafka-ui
    app.kubernetes.io/component: oauth2-credentials
type: Opaque
data:
  # Google OAuth2 Client ID (base64 encoded)
  # Original: 867203584040-j7pfakbprggkdn8b8iseglm7ivpbqcuo.apps.googleusercontent.com
  client-id: ODY3MjAzNTg0MDQwLWo3cGZha2Jwcmdna2RuOGI4aXNlZ2xtN2l2cGJxY3VvLmFwcHMuZ29vZ2xldXNlcmNvbnRlbnQuY29t
  
  # Google OAuth2 Client Secret (base64 encoded)
  # Original: 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
  client-secret: R09DU1BYLWVKSG96R2JITFYzQnlFZmZIb08xUDl0cW5zN0s=

---
# Alternative: Create secret using stringData (more readable, but less secure in version control)
apiVersion: v1
kind: Secret
metadata:
  name: kafka-ui-oauth2-secret-stringdata
  namespace: default  # Change to your target namespace
  labels:
    app.kubernetes.io/name: kafka-ui
    app.kubernetes.io/component: oauth2-credentials
type: Opaque
stringData:
  client-id: "867203584040-j7pfakbprggkdn8b8iseglm7ivpbqcuo.apps.googleusercontent.com"
  client-secret: "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"
