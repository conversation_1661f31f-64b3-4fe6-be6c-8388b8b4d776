{{- if and .Values.networkPolicy.enabled .Values.networkPolicy.ingressRules.customRules }}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ printf "%s-ingress" (include "kafka-ui.fullname" .) }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "kafka-ui.labels" . | nindent 4 }}
spec:
  podSelector:
    matchLabels:
      {{- include "kafka-ui.selectorLabels" . | nindent 6 }}
  policyTypes:
    - Ingress
  ingress:
    {{- if .Values.networkPolicy.ingressRules.customRules }}
    {{- tpl (toYaml .Values.networkPolicy.ingressRules.customRules) $ | nindent 4 }}
    {{- end }}
{{- end }}
