{{- if and .Values.networkPolicy.enabled .Values.networkPolicy.egressRules.customRules }}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ printf "%s-egress" (include "kafka-ui.fullname" .) }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "kafka-ui.labels" . | nindent 4 }}
spec:
  podSelector:
    matchLabels:
      {{- include "kafka-ui.selectorLabels" . | nindent 6 }}
  policyTypes:
    - Egress
  egress:
    {{- if .Values.networkPolicy.egressRules.customRules }}
    {{- tpl (toYaml .Values.networkPolicy.egressRules.customRules) $ | nindent 4 }}
    {{- end }}
{{- end }}
