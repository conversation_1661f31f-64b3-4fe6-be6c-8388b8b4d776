{{- if .Values.ingress.enabled -}}
{{- $fullName := include "kafka-ui.fullname" . -}}
{{- $svcPort := .Values.service.port -}}
{{- $kubeCapabilityVersion := semver .Capabilities.KubeVersion.Version -}}
{{- $isHigher1p19 := ge (semver "1.19" | $kubeCapabilityVersion.Compare) 0 -}}
{{- if and ($.Capabilities.APIVersions.Has "networking.k8s.io/v1") $isHigher1p19 -}}
apiVersion: networking.k8s.io/v1
{{- else if $.Capabilities.APIVersions.Has "networking.k8s.io/v1beta1" }}
apiVersion: networking.k8s.io/v1beta1
{{- else }}
apiVersion: extensions/v1beta1
{{- end }}
kind: Ingress
metadata:
  name: {{ $fullName }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "kafka-ui.labels" . | nindent 4 }}
    {{- with .Values.ingress.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if .Values.ingress.tls.enabled }}
  tls:
    - hosts:
        - {{ tpl .Values.ingress.host . }}
      secretName: {{ .Values.ingress.tls.secretName }}
  {{- end }}
  {{- if .Values.ingress.ingressClassName }}
  ingressClassName: {{ .Values.ingress.ingressClassName }}
  {{- end }}
  rules:
    - http:
        paths:
{{- if and ($.Capabilities.APIVersions.Has "networking.k8s.io/v1") $isHigher1p19 -}}
          {{- range .Values.ingress.precedingPaths }}
          - path: {{ .path }}
            pathType: {{ .pathType }}
            backend:
              service:
                name: {{ .serviceName }}
                port:
                  {{- if .servicePort }}
                  number: {{ .servicePort }}
                  {{- end }}
                  {{- if .servicePortName }}
                  name: {{ .servicePortName }}
                  {{- end }}
          {{- end }}
          - backend:
              service:
                name: {{ $fullName }}
                port:
                  number: {{ $svcPort }}
            pathType: {{ .Values.ingress.pathType }}
{{- if .Values.ingress.path }}
            path: {{ .Values.ingress.path }}
{{- end }}
          {{- range .Values.ingress.succeedingPaths }}
          - path: {{ .path }}
            pathType: {{ .pathType }}
            backend:
              service:
                name: {{ .serviceName }}
                port:
                  number: {{ .servicePort }}
          {{- end }}
{{- if tpl .Values.ingress.host . }}
      host: {{tpl .Values.ingress.host . }}
{{- end }}
{{- else -}}
          {{- range .Values.ingress.precedingPaths }}
          - path: {{ .path }}
            backend:
              serviceName: {{ .serviceName }}
              servicePort: {{ .servicePort }}
          {{- end }}
          - backend:
              serviceName: {{ $fullName }}
              servicePort: {{ $svcPort }}
{{- if .Values.ingress.path }}
            path: {{ .Values.ingress.path }}
{{- end }}
          {{- range .Values.ingress.succeedingPaths }}
          - path: {{ .path }}
            backend:
              serviceName: {{ .serviceName }}
              servicePort: {{ .servicePort }}
          {{- end }}
{{- if tpl .Values.ingress.host . }}
      host: {{ tpl .Values.ingress.host . }}
{{- end }}
{{- end }}
{{- end }}
