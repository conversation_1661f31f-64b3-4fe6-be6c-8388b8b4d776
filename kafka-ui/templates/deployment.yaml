apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "kafka-ui.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "kafka-ui.labels" . | nindent 4 }}
    {{- if .Values.labels }}
    {{- toYaml .Values.labels | nindent 4 }}
    {{- end }}
  {{- with .Values.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
{{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
{{- end }}
  selector:
    matchLabels:
      {{- include "kafka-ui.selectorLabels" . | nindent 6 }}
  {{- if .Values.revisionHistoryLimit }}
  revisionHistoryLimit: {{ .Values.revisionHistoryLimit }}
  {{- end }}
  template:
    metadata:
      annotations:
      {{- with .Values.podAnnotations }}
          {{- toYaml . | nindent 8 }}
      {{- end }}
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        checksum/configFromValues: {{ include (print $.Template.BasePath "/configmap_fromValues.yaml") . | sha256sum }}
        checksum/secret: {{ include (print $.Template.BasePath "/secret.yaml") . | sha256sum }}
      labels:
        {{- include "kafka-ui.selectorLabels" . | nindent 8 }}
        {{- if .Values.podLabels }}
        {{- toYaml .Values.podLabels | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.initContainers }}
      initContainers:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "kafka-ui.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: {{ include "kafka-ui.imageName" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          {{- if or .Values.env
              .Values.yamlApplicationConfig .Values.yamlApplicationConfigConfigMap .Values.yamlApplicationConfigSecret
              (.Values.envs).secretMappings (.Values.envs).configMappings
          }}
          env:
            {{- with .Values.env }}
              {{- toYaml . | nindent 12 }}
            {{- end }}
            {{- if or .Values.yamlApplicationConfig .Values.yamlApplicationConfigConfigMap .Values.yamlApplicationConfigSecret }}
            - name: SPRING_CONFIG_ADDITIONAL-LOCATION
              {{- if .Values.yamlApplicationConfig }}
              value: /kafka-ui/config.yml
              {{- else if .Values.yamlApplicationConfigConfigMap }}
              value: /kafka-ui/{{ .Values.yamlApplicationConfigConfigMap.keyName | default "config.yml" }}
              {{- else if .Values.yamlApplicationConfigSecret }}
              value: /kafka-ui/{{ .Values.yamlApplicationConfigSecret.keyName | default "config.yml" }}
              {{- end }}
            {{- end }}
            {{- range $key, $value := .Values.envs.secretMappings }}
            - name: {{ $key }}
              valueFrom:
                secretKeyRef:
                  name: {{ required "Missing required value envs.secretMappings.[].name" $value.name }}
                  key: {{ required "Missing required value envs.secretMappings.[].keyName"  $value.keyName }}
            {{- end }}
            {{- range $key, $value := .Values.envs.configMappings }}
            - name: {{ $key }}
              valueFrom:
                configMapKeyRef:
                  name: {{ required "Missing required value envs.configMappings.[].name" $value.name }}
                  key: {{ required "Missing required value envs.configMappings.[].keyName"  $value.keyName }}
            {{- end }}
          {{- end }}
          {{- if or .Values.existingConfigMap .Values.envs.config .Values.existingSecret .Values.envs.secret }}
          envFrom:
            {{- if .Values.existingConfigMap }}
            - configMapRef:
                name: {{ .Values.existingConfigMap }}
            {{- end }}
            {{- if .Values.envs.config }}
            - configMapRef:
                name: {{ include "kafka-ui.fullname" . }}
            {{- end }}
            {{- if .Values.existingSecret }}
            - secretRef:
                name: {{ .Values.existingSecret }}
            {{- end }}
            {{- if .Values.envs.secret}}
            - secretRef:
                name: {{ include "kafka-ui.fullname" . }}
            {{- end}}
          {{- end }}
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          livenessProbe:
            httpGet:
              {{- $contextPath := .Values.envs.config.SERVER_SERVLET_CONTEXT_PATH | default "" | printf "%s/actuator/health" | urlParse }}
              path: {{ get $contextPath "path" }}
              port: http
              {{- if .Values.probes.useHttpsScheme }}
              scheme: HTTPS
              {{- end }}
            initialDelaySeconds: 60
            periodSeconds: 30
            timeoutSeconds: 10
          readinessProbe:
            httpGet:
              {{- $contextPath := .Values.envs.config.SERVER_SERVLET_CONTEXT_PATH | default "" | printf "%s/actuator/health" | urlParse }}
              path: {{ get $contextPath "path" }}
              port: http
              {{- if .Values.probes.useHttpsScheme }}
              scheme: HTTPS
              {{- end }}
            initialDelaySeconds: 60
            periodSeconds: 30
            timeoutSeconds: 10
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          {{- if or .Values.yamlApplicationConfig .Values.volumeMounts .Values.yamlApplicationConfigConfigMap .Values.yamlApplicationConfigSecret }}
          volumeMounts:
            {{- with .Values.volumeMounts }} 
              {{- toYaml . | nindent 12 }}
            {{- end }}
            {{- if .Values.yamlApplicationConfig }}
            - name: kafka-ui-yaml-conf
              mountPath: /kafka-ui/
            {{- end }}
            {{- if .Values.yamlApplicationConfigConfigMap}}
            - name: kafka-ui-yaml-conf-configmap
              mountPath: /kafka-ui/
            {{- end }}
            {{- if .Values.yamlApplicationConfigSecret}}
            - name: kafka-ui-yaml-conf-secret
              mountPath: /kafka-ui/
            {{- end }}
          {{- end }}
        {{- with .Values.extraContainers }}
        {{ tpl . $ | nindent 8 }}
        {{- end }}
      {{- if or .Values.yamlApplicationConfig .Values.volumes .Values.yamlApplicationConfigConfigMap .Values.yamlApplicationConfigSecret }}
      volumes:
        {{- with .Values.volumes }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
        {{- if .Values.yamlApplicationConfig }}
        - name: kafka-ui-yaml-conf
          configMap: 
            name: {{ include "kafka-ui.fullname" . }}-fromvalues
        {{- end }}
        {{- if .Values.yamlApplicationConfigConfigMap}}
        - name: kafka-ui-yaml-conf-configmap
          configMap: 
            name: {{ .Values.yamlApplicationConfigConfigMap.name }}
        {{- end }}
        {{- if .Values.yamlApplicationConfigSecret}}
        - name: kafka-ui-yaml-conf-secret
          secret:
            secretName: {{ .Values.yamlApplicationConfigSecret.name }}
        {{- end }}
      {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.hostAliases }}
      hostAliases:
        {{- toYaml . | nindent 8 }}
      {{- end }}
