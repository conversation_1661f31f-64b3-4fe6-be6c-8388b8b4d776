apiVersion: v1
kind: Service
metadata:
  name: {{ include "kafka-ui.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "kafka-ui.labels" . | nindent 4 }}
    {{- with .Values.service.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
{{- if .Values.service.annotations }}
  annotations:
{{ toYaml .Values.service.annotations | nindent 4 }}
{{- end }}
spec:
  type: {{ .Values.service.type }}
{{- if and (eq .Values.service.type "LoadBalancer") (.Values.service.loadBalancerIP) }}
  loadBalancerIP: {{ .Values.service.loadBalancerIP }}
{{- end }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
      {{- if (and (eq .Values.service.type "NodePort") .Values.service.nodePort) }}
      nodePort: {{ .Values.service.nodePort }}
      {{- end }}
  selector:
    {{- include "kafka-ui.selectorLabels" . | nindent 4 }}
