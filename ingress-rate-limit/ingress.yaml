apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: nginx-demo-ingress
  annotations:
    nginx.ingress.kubernetes.io/limit-rps: "1"                      
    nginx.ingress.kubernetes.io/limit-rpm: "5"                   
    nginx.ingress.kubernetes.io/limit-connections: "2"              
    nginx.ingress.kubernetes.io/limit-burst-multiplier: "3"         
    # nginx.ingress.kubernetes.io/limit-whitelist: "10.0.0.0/8"     
    nginx.ingress.kubernetes.io/limit-req-status-code: "429"   
spec:
  rules:
    - host: nginx-demo.strawmine.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: nginx-demo
                port:
                  number: 80
